{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/analytics.edit": {"description": "Edit Google Analytics management entities"}, "https://www.googleapis.com/auth/analytics.manage.users": {"description": "Manage Google Analytics Account users by email address"}, "https://www.googleapis.com/auth/analytics.manage.users.readonly": {"description": "View Google Analytics user permissions"}, "https://www.googleapis.com/auth/analytics.readonly": {"description": "See and download your Google Analytics data"}}}}, "basePath": "", "baseUrl": "https://analyticsadmin.googleapis.com/", "batchPath": "batch", "canonicalName": "Google Analytics Admin", "description": "Manage properties in Google Analytics. Warning: Creating multiple Customer Applications, Accounts, or Projects to simulate or act as a single Customer Application, Account, or Project (respectively) or to circumvent Service-specific usage limits or quotas is a direct violation of Google Cloud Platform Terms of Service as well as Google APIs Terms of Service. These actions can result in immediate termination of your GCP project(s) without any warning.", "discoveryVersion": "v1", "documentationLink": "http://code.google.com/apis/analytics/docs/mgmt/home.html", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "analyticsadmin:v1alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://analyticsadmin.mtls.googleapis.com/", "name": "analyticsadmin", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accountSummaries": {"methods": {"list": {"description": "Returns summaries of all accounts accessible by the caller.", "flatPath": "v1alpha/accountSummaries", "httpMethod": "GET", "id": "analyticsadmin.accountSummaries.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of AccountSummary resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAccountSummaries` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccountSummaries` must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "v1alpha/accountSummaries", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListAccountSummariesResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}}}, "accounts": {"methods": {"delete": {"description": "Marks target Account as soft-deleted (ie: \"trashed\") and returns it. This API does not have a method to restore soft-deleted accounts. However, they can be restored using the Trash Can UI. If the accounts are not restored before the expiration time, the account and all child resources (eg: Properties, GoogleAdsLinks, Streams, AccessBindings) will be permanently purged. https://support.google.com/analytics/answer/6154772 Returns an error if the target is not found.", "flatPath": "v1alpha/accounts/{accountsId}", "httpMethod": "DELETE", "id": "analyticsadmin.accounts.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Account to soft-delete. Format: accounts/{account} Example: \"accounts/100\"", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single Account.", "flatPath": "v1alpha/accounts/{accountsId}", "httpMethod": "GET", "id": "analyticsadmin.accounts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the account to lookup. Format: accounts/{account} Example: \"accounts/100\"", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaAccount"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getDataSharingSettings": {"description": "Get data sharing settings on an account. Data sharing settings are singletons.", "flatPath": "v1alpha/accounts/{accountsId}/dataSharingSettings", "httpMethod": "GET", "id": "analyticsadmin.accounts.getDataSharingSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the settings to lookup. Format: accounts/{account}/dataSharingSettings Example: `accounts/1000/dataSharingSettings`", "location": "path", "pattern": "^accounts/[^/]+/dataSharingSettings$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaDataSharingSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Returns all accounts accessible by the caller. Note that these accounts might not currently have GA properties. Soft-deleted (ie: \"trashed\") accounts are excluded by default. Returns an empty list if no relevant accounts are found.", "flatPath": "v1alpha/accounts", "httpMethod": "GET", "id": "analyticsadmin.accounts.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAccounts` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccounts` must match the call that provided the page token.", "location": "query", "type": "string"}, "showDeleted": {"description": "Whether to include soft-deleted (ie: \"trashed\") Accounts in the results. Accounts can be inspected to determine whether they are deleted or not.", "location": "query", "type": "boolean"}}, "path": "v1alpha/accounts", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates an account.", "flatPath": "v1alpha/accounts/{accountsId}", "httpMethod": "PATCH", "id": "analyticsadmin.accounts.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this account. Format: accounts/{account} Example: \"accounts/100\"", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (for example, \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAccount"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAccount"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "provisionAccountTicket": {"description": "Requests a ticket for creating an account.", "flatPath": "v1alpha/accounts:provisionAccountTicket", "httpMethod": "POST", "id": "analyticsadmin.accounts.provisionAccountTicket", "parameterOrder": [], "parameters": {}, "path": "v1alpha/accounts:provisionAccountTicket", "request": {"$ref": "GoogleAnalyticsAdminV1alphaProvisionAccountTicketRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaProvisionAccountTicketResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "runAccessReport": {"description": "Returns a customized report of data access records. The report provides records of each time a user reads Google Analytics reporting data. Access records are retained for up to 2 years. Data Access Reports can be requested for a property. Reports may be requested for any property, but dimensions that aren't related to quota can only be requested on Google Analytics 360 properties. This method is only available to Administrators. These data access records include GA UI Reporting, GA UI Explorations, GA Data API, and other products like Firebase & Admob that can retrieve data from Google Analytics through a linkage. These records don't include property configuration changes like adding a stream or changing a property's time zone. For configuration change history, see [searchChangeHistoryEvents](https://developers.google.com/analytics/devguides/config/admin/v1/rest/v1alpha/accounts/searchChangeHistoryEvents). To give your feedback on this API, complete the [Google Analytics Access Reports feedback](https://docs.google.com/forms/d/e/1FAIpQLSdmEBUrMzAEdiEKk5TV5dEHvDUZDRlgWYdQdAeSdtR4hVjEhw/viewform) form.", "flatPath": "v1alpha/accounts/{accountsId}:runAccessReport", "httpMethod": "POST", "id": "analyticsadmin.accounts.runAccessReport", "parameterOrder": ["entity"], "parameters": {"entity": {"description": "The Data Access Report supports requesting at the property level or account level. If requested at the account level, Data Access Reports include all access for all properties under that account. To request at the property level, entity should be for example 'properties/123' if \"123\" is your Google Analytics property ID. To request at the account level, entity should be for example 'accounts/1234' if \"1234\" is your Google Analytics Account ID.", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+entity}:runAccessReport", "request": {"$ref": "GoogleAnalyticsAdminV1alphaRunAccessReportRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaRunAccessReportResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "searchChangeHistoryEvents": {"description": "Searches through all changes to an account or its children given the specified set of filters. Only returns the subset of changes supported by the API. The UI may return additional changes.", "flatPath": "v1alpha/accounts/{accountsId}:searchChangeHistoryEvents", "httpMethod": "POST", "id": "analyticsadmin.accounts.searchChangeHistoryEvents", "parameterOrder": ["account"], "parameters": {"account": {"description": "Required. The account resource for which to return change history resources. Format: accounts/{account} Example: `accounts/100`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+account}:searchChangeHistoryEvents", "request": {"$ref": "GoogleAnalyticsAdminV1alphaSearchChangeHistoryEventsRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaSearchChangeHistoryEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}, "resources": {"accessBindings": {"methods": {"batchCreate": {"description": "Creates information about multiple access bindings to an account or property. This method is transactional. If any AccessBinding cannot be created, none of the AccessBindings will be created.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings:batchCreate", "httpMethod": "POST", "id": "analyticsadmin.accounts.accessBindings.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account or property that owns the access bindings. The parent field in the CreateAccessBindingRequest messages must either be empty or match this field. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings:batchCreate", "request": {"$ref": "GoogleAnalyticsAdminV1alphaBatchCreateAccessBindingsRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaBatchCreateAccessBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "batchDelete": {"description": "Deletes information about multiple users' links to an account or property.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings:batchDelete", "httpMethod": "POST", "id": "analyticsadmin.accounts.accessBindings.batchDelete", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account or property that owns the access bindings. The parent of all provided values for the 'names' field in DeleteAccessBindingRequest messages must match this field. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings:batchDelete", "request": {"$ref": "GoogleAnalyticsAdminV1alphaBatchDeleteAccessBindingsRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "batchGet": {"description": "Gets information about multiple access bindings to an account or property.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings:batchGet", "httpMethod": "GET", "id": "analyticsadmin.accounts.accessBindings.batchGet", "parameterOrder": ["parent"], "parameters": {"names": {"description": "Required. The names of the access bindings to retrieve. A maximum of 1000 access bindings can be retrieved in a batch. Formats: - accounts/{account}/accessBindings/{accessBinding} - properties/{property}/accessBindings/{accessBinding}", "location": "query", "repeated": true, "type": "string"}, "parent": {"description": "Required. The account or property that owns the access bindings. The parent of all provided values for the 'names' field must match this field. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings:batchGet", "response": {"$ref": "GoogleAnalyticsAdminV1alphaBatchGetAccessBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users", "https://www.googleapis.com/auth/analytics.manage.users.readonly"]}, "batchUpdate": {"description": "Updates information about multiple access bindings to an account or property.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings:batchUpdate", "httpMethod": "POST", "id": "analyticsadmin.accounts.accessBindings.batchUpdate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account or property that owns the access bindings. The parent of all provided AccessBinding in UpdateAccessBindingRequest messages must match this field. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings:batchUpdate", "request": {"$ref": "GoogleAnalyticsAdminV1alphaBatchUpdateAccessBindingsRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaBatchUpdateAccessBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "create": {"description": "Creates an access binding on an account or property.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings", "httpMethod": "POST", "id": "analyticsadmin.accounts.accessBindings.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "delete": {"description": "Deletes an access binding on an account or property.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings/{accessBindingsId}", "httpMethod": "DELETE", "id": "analyticsadmin.accounts.accessBindings.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Formats: - accounts/{account}/accessBindings/{accessBinding} - properties/{property}/accessBindings/{accessBinding}", "location": "path", "pattern": "^accounts/[^/]+/accessBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "get": {"description": "Gets information about an access binding.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings/{accessBindingsId}", "httpMethod": "GET", "id": "analyticsadmin.accounts.accessBindings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the access binding to retrieve. Formats: - accounts/{account}/accessBindings/{accessBinding} - properties/{property}/accessBindings/{accessBinding}", "location": "path", "pattern": "^accounts/[^/]+/accessBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users", "https://www.googleapis.com/auth/analytics.manage.users.readonly"]}, "list": {"description": "Lists all access bindings on an account or property.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings", "httpMethod": "GET", "id": "analyticsadmin.accounts.accessBindings.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of access bindings to return. The service may return fewer than this value. If unspecified, at most 200 access bindings will be returned. The maximum value is 500; values above 500 will be coerced to 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAccessBindings` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccessBindings` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListAccessBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users", "https://www.googleapis.com/auth/analytics.manage.users.readonly"]}, "patch": {"description": "Updates an access binding on an account or property.", "flatPath": "v1alpha/accounts/{accountsId}/accessBindings/{accessBindingsId}", "httpMethod": "PATCH", "id": "analyticsadmin.accounts.accessBindings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this binding. Format: accounts/{account}/accessBindings/{access_binding} or properties/{property}/accessBindings/{access_binding} Example: \"accounts/100/accessBindings/200\"", "location": "path", "pattern": "^accounts/[^/]+/accessBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}}}}}, "properties": {"methods": {"acknowledgeUserDataCollection": {"description": "Acknowledges the terms of user data collection for the specified property. This acknowledgement must be completed (either in the Google Analytics UI or through this API) before MeasurementProtocolSecret resources may be created.", "flatPath": "v1alpha/properties/{propertiesId}:acknowledgeUserDataCollection", "httpMethod": "POST", "id": "analyticsadmin.properties.acknowledgeUserDataCollection", "parameterOrder": ["property"], "parameters": {"property": {"description": "Required. The property for which to acknowledge user data collection.", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+property}:acknowledgeUserDataCollection", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAcknowledgeUserDataCollectionRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAcknowledgeUserDataCollectionResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "create": {"description": "Creates a Google Analytics property with the specified location and attributes.", "flatPath": "v1alpha/properties", "httpMethod": "POST", "id": "analyticsadmin.properties.create", "parameterOrder": [], "parameters": {}, "path": "v1alpha/properties", "request": {"$ref": "GoogleAnalyticsAdminV1alphaProperty"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaProperty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "createConnectedSiteTag": {"description": "Creates a connected site tag for a Universal Analytics property. You can create a maximum of 20 connected site tags per property. Note: This API cannot be used on GA4 properties.", "flatPath": "v1alpha/properties:createConnectedSiteTag", "httpMethod": "POST", "id": "analyticsadmin.properties.createConnectedSiteTag", "parameterOrder": [], "parameters": {}, "path": "v1alpha/properties:createConnectedSiteTag", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCreateConnectedSiteTagRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaCreateConnectedSiteTagResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "createRollupProperty": {"description": "Create a roll-up property and all roll-up property source links.", "flatPath": "v1alpha/properties:createRollupProperty", "httpMethod": "POST", "id": "analyticsadmin.properties.createRollupProperty", "parameterOrder": [], "parameters": {}, "path": "v1alpha/properties:createRollupProperty", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCreateRollupPropertyRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaCreateRollupPropertyResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Marks target Property as soft-deleted (ie: \"trashed\") and returns it. This API does not have a method to restore soft-deleted properties. However, they can be restored using the Trash Can UI. If the properties are not restored before the expiration time, the Property and all child resources (eg: GoogleAdsLinks, Streams, AccessBindings) will be permanently purged. https://support.google.com/analytics/answer/6154772 Returns an error if the target is not found.", "flatPath": "v1alpha/properties/{propertiesId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Property to soft-delete. Format: properties/{property_id} Example: \"properties/1000\"", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaProperty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "deleteConnectedSiteTag": {"description": "Deletes a connected site tag for a Universal Analytics property. Note: this has no effect on GA4 properties.", "flatPath": "v1alpha/properties:deleteConnectedSiteTag", "httpMethod": "POST", "id": "analyticsadmin.properties.deleteConnectedSiteTag", "parameterOrder": [], "parameters": {}, "path": "v1alpha/properties:deleteConnectedSiteTag", "request": {"$ref": "GoogleAnalyticsAdminV1alphaDeleteConnectedSiteTagRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "fetchAutomatedGa4ConfigurationOptOut": {"description": "Fetches the opt out status for the automated GA4 setup process for a UA property. Note: this has no effect on GA4 property.", "flatPath": "v1alpha/properties:fetchAutomatedGa4ConfigurationOptOut", "httpMethod": "POST", "id": "analyticsadmin.properties.fetchAutomatedGa4ConfigurationOptOut", "parameterOrder": [], "parameters": {}, "path": "v1alpha/properties:fetchAutomatedGa4ConfigurationOptOut", "request": {"$ref": "GoogleAnalyticsAdminV1alphaFetchAutomatedGa4ConfigurationOptOutRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaFetchAutomatedGa4ConfigurationOptOutResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "fetchConnectedGa4Property": {"description": "Given a specified UA property, looks up the GA4 property connected to it. Note: this cannot be used with GA4 properties.", "flatPath": "v1alpha/properties:fetchConnectedGa4Property", "httpMethod": "GET", "id": "analyticsadmin.properties.fetchConnectedGa4Property", "parameterOrder": [], "parameters": {"property": {"description": "Required. The UA property for which to look up the connected GA4 property. Note this request uses the internal property ID, not the tracking ID of the form UA-XXXXXX-YY. Format: properties/{internal_web_property_id} Example: properties/1234", "location": "query", "type": "string"}}, "path": "v1alpha/properties:fetchConnectedGa4Property", "response": {"$ref": "GoogleAnalyticsAdminV1alphaFetchConnectedGa4PropertyResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "get": {"description": "Lookup for a single GA Property.", "flatPath": "v1alpha/properties/{propertiesId}", "httpMethod": "GET", "id": "analyticsadmin.properties.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the property to lookup. Format: properties/{property_id} Example: \"properties/1000\"", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaProperty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getAttributionSettings": {"description": "Lookup for a AttributionSettings singleton.", "flatPath": "v1alpha/properties/{propertiesId}/attributionSettings", "httpMethod": "GET", "id": "analyticsadmin.properties.getAttributionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the attribution settings to retrieve. Format: properties/{property}/attributionSettings", "location": "path", "pattern": "^properties/[^/]+/attributionSettings$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaAttributionSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getDataRetentionSettings": {"description": "Returns the singleton data retention settings for this property.", "flatPath": "v1alpha/properties/{propertiesId}/dataRetentionSettings", "httpMethod": "GET", "id": "analyticsadmin.properties.getDataRetentionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the settings to lookup. Format: properties/{property}/dataRetentionSettings Example: \"properties/1000/dataRetentionSettings\"", "location": "path", "pattern": "^properties/[^/]+/dataRetentionSettings$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaDataRetentionSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getGoogleSignalsSettings": {"description": "Lookup for Google Signals settings for a property.", "flatPath": "v1alpha/properties/{propertiesId}/googleSignalsSettings", "httpMethod": "GET", "id": "analyticsadmin.properties.getGoogleSignalsSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the google signals settings to retrieve. Format: properties/{property}/googleSignalsSettings", "location": "path", "pattern": "^properties/[^/]+/googleSignalsSettings$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleSignalsSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Returns child Properties under the specified parent Account. Properties will be excluded if the caller does not have access. Soft-deleted (ie: \"trashed\") properties are excluded by default. Returns an empty list if no relevant properties are found.", "flatPath": "v1alpha/properties", "httpMethod": "GET", "id": "analyticsadmin.properties.list", "parameterOrder": [], "parameters": {"filter": {"description": "Required. An expression for filtering the results of the request. Fields eligible for filtering are: `parent:`(The resource name of the parent account/property) or `ancestor:`(The resource name of the parent account) or `firebase_project:`(The id or number of the linked firebase project). Some examples of filters: ``` | Filter | Description | |-----------------------------|-------------------------------------------| | parent:accounts/123 | The account with account id: 123. | | parent:properties/123 | The property with property id: 123. | | ancestor:accounts/123 | The account with account id: 123. | | firebase_project:project-id | The firebase project with id: project-id. | | firebase_project:123 | The firebase project with number: 123. | ```", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListProperties` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListProperties` must match the call that provided the page token.", "location": "query", "type": "string"}, "showDeleted": {"description": "Whether to include soft-deleted (ie: \"trashed\") Properties in the results. Properties can be inspected to determine whether they are deleted or not.", "location": "query", "type": "boolean"}}, "path": "v1alpha/properties", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListPropertiesResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "listConnectedSiteTags": {"description": "Lists the connected site tags for a Universal Analytics property. A maximum of 20 connected site tags will be returned. Note: this has no effect on GA4 property.", "flatPath": "v1alpha/properties:listConnectedSiteTags", "httpMethod": "POST", "id": "analyticsadmin.properties.listConnectedSiteTags", "parameterOrder": [], "parameters": {}, "path": "v1alpha/properties:listConnectedSiteTags", "request": {"$ref": "GoogleAnalyticsAdminV1alphaListConnectedSiteTagsRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaListConnectedSiteTagsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a property.", "flatPath": "v1alpha/properties/{propertiesId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this property. Format: properties/{property_id} Example: \"properties/1000\"", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaProperty"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaProperty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "provisionSubproperty": {"description": "Create a subproperty and a subproperty event filter that applies to the created subproperty.", "flatPath": "v1alpha/properties:provisionSubproperty", "httpMethod": "POST", "id": "analyticsadmin.properties.provisionSubproperty", "parameterOrder": [], "parameters": {}, "path": "v1alpha/properties:provisionSubproperty", "request": {"$ref": "GoogleAnalyticsAdminV1alphaProvisionSubpropertyRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaProvisionSubpropertyResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "runAccessReport": {"description": "Returns a customized report of data access records. The report provides records of each time a user reads Google Analytics reporting data. Access records are retained for up to 2 years. Data Access Reports can be requested for a property. Reports may be requested for any property, but dimensions that aren't related to quota can only be requested on Google Analytics 360 properties. This method is only available to Administrators. These data access records include GA UI Reporting, GA UI Explorations, GA Data API, and other products like Firebase & Admob that can retrieve data from Google Analytics through a linkage. These records don't include property configuration changes like adding a stream or changing a property's time zone. For configuration change history, see [searchChangeHistoryEvents](https://developers.google.com/analytics/devguides/config/admin/v1/rest/v1alpha/accounts/searchChangeHistoryEvents). To give your feedback on this API, complete the [Google Analytics Access Reports feedback](https://docs.google.com/forms/d/e/1FAIpQLSdmEBUrMzAEdiEKk5TV5dEHvDUZDRlgWYdQdAeSdtR4hVjEhw/viewform) form.", "flatPath": "v1alpha/properties/{propertiesId}:runAccessReport", "httpMethod": "POST", "id": "analyticsadmin.properties.runAccessReport", "parameterOrder": ["entity"], "parameters": {"entity": {"description": "The Data Access Report supports requesting at the property level or account level. If requested at the account level, Data Access Reports include all access for all properties under that account. To request at the property level, entity should be for example 'properties/123' if \"123\" is your Google Analytics property ID. To request at the account level, entity should be for example 'accounts/1234' if \"1234\" is your Google Analytics Account ID.", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+entity}:runAccessReport", "request": {"$ref": "GoogleAnalyticsAdminV1alphaRunAccessReportRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaRunAccessReportResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "setAutomatedGa4ConfigurationOptOut": {"description": "Sets the opt out status for the automated GA4 setup process for a UA property. Note: this has no effect on GA4 property.", "flatPath": "v1alpha/properties:setAutomatedGa4ConfigurationOptOut", "httpMethod": "POST", "id": "analyticsadmin.properties.setAutomatedGa4ConfigurationOptOut", "parameterOrder": [], "parameters": {}, "path": "v1alpha/properties:setAutomatedGa4ConfigurationOptOut", "request": {"$ref": "GoogleAnalyticsAdminV1alphaSetAutomatedGa4ConfigurationOptOutRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaSetAutomatedGa4ConfigurationOptOutResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "updateAttributionSettings": {"description": "Updates attribution settings on a property.", "flatPath": "v1alpha/properties/{propertiesId}/attributionSettings", "httpMethod": "PATCH", "id": "analyticsadmin.properties.updateAttributionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this attribution settings resource. Format: properties/{property_id}/attributionSettings Example: \"properties/1000/attributionSettings\"", "location": "path", "pattern": "^properties/[^/]+/attributionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAttributionSettings"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAttributionSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "updateDataRetentionSettings": {"description": "Updates the singleton data retention settings for this property.", "flatPath": "v1alpha/properties/{propertiesId}/dataRetentionSettings", "httpMethod": "PATCH", "id": "analyticsadmin.properties.updateDataRetentionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name for this DataRetentionSetting resource. Format: properties/{property}/dataRetentionSettings", "location": "path", "pattern": "^properties/[^/]+/dataRetentionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaDataRetentionSettings"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaDataRetentionSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "updateGoogleSignalsSettings": {"description": "Updates Google Signals settings for a property.", "flatPath": "v1alpha/properties/{propertiesId}/googleSignalsSettings", "httpMethod": "PATCH", "id": "analyticsadmin.properties.updateGoogleSignalsSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this setting. Format: properties/{property_id}/googleSignalsSettings Example: \"properties/1000/googleSignalsSettings\"", "location": "path", "pattern": "^properties/[^/]+/googleSignalsSettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleSignalsSettings"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleSignalsSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}, "resources": {"accessBindings": {"methods": {"batchCreate": {"description": "Creates information about multiple access bindings to an account or property. This method is transactional. If any AccessBinding cannot be created, none of the AccessBindings will be created.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings:batchCreate", "httpMethod": "POST", "id": "analyticsadmin.properties.accessBindings.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account or property that owns the access bindings. The parent field in the CreateAccessBindingRequest messages must either be empty or match this field. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings:batchCreate", "request": {"$ref": "GoogleAnalyticsAdminV1alphaBatchCreateAccessBindingsRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaBatchCreateAccessBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "batchDelete": {"description": "Deletes information about multiple users' links to an account or property.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings:batchDelete", "httpMethod": "POST", "id": "analyticsadmin.properties.accessBindings.batchDelete", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account or property that owns the access bindings. The parent of all provided values for the 'names' field in DeleteAccessBindingRequest messages must match this field. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings:batchDelete", "request": {"$ref": "GoogleAnalyticsAdminV1alphaBatchDeleteAccessBindingsRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "batchGet": {"description": "Gets information about multiple access bindings to an account or property.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings:batchGet", "httpMethod": "GET", "id": "analyticsadmin.properties.accessBindings.batchGet", "parameterOrder": ["parent"], "parameters": {"names": {"description": "Required. The names of the access bindings to retrieve. A maximum of 1000 access bindings can be retrieved in a batch. Formats: - accounts/{account}/accessBindings/{accessBinding} - properties/{property}/accessBindings/{accessBinding}", "location": "query", "repeated": true, "type": "string"}, "parent": {"description": "Required. The account or property that owns the access bindings. The parent of all provided values for the 'names' field must match this field. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings:batchGet", "response": {"$ref": "GoogleAnalyticsAdminV1alphaBatchGetAccessBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users", "https://www.googleapis.com/auth/analytics.manage.users.readonly"]}, "batchUpdate": {"description": "Updates information about multiple access bindings to an account or property.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings:batchUpdate", "httpMethod": "POST", "id": "analyticsadmin.properties.accessBindings.batchUpdate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account or property that owns the access bindings. The parent of all provided AccessBinding in UpdateAccessBindingRequest messages must match this field. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings:batchUpdate", "request": {"$ref": "GoogleAnalyticsAdminV1alphaBatchUpdateAccessBindingsRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaBatchUpdateAccessBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "create": {"description": "Creates an access binding on an account or property.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings", "httpMethod": "POST", "id": "analyticsadmin.properties.accessBindings.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "delete": {"description": "Deletes an access binding on an account or property.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings/{accessBindingsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.accessBindings.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Formats: - accounts/{account}/accessBindings/{accessBinding} - properties/{property}/accessBindings/{accessBinding}", "location": "path", "pattern": "^properties/[^/]+/accessBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}, "get": {"description": "Gets information about an access binding.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings/{accessBindingsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.accessBindings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the access binding to retrieve. Formats: - accounts/{account}/accessBindings/{accessBinding} - properties/{property}/accessBindings/{accessBinding}", "location": "path", "pattern": "^properties/[^/]+/accessBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users", "https://www.googleapis.com/auth/analytics.manage.users.readonly"]}, "list": {"description": "Lists all access bindings on an account or property.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings", "httpMethod": "GET", "id": "analyticsadmin.properties.accessBindings.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of access bindings to return. The service may return fewer than this value. If unspecified, at most 200 access bindings will be returned. The maximum value is 500; values above 500 will be coerced to 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAccessBindings` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccessBindings` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Formats: - accounts/{account} - properties/{property}", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/accessBindings", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListAccessBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users", "https://www.googleapis.com/auth/analytics.manage.users.readonly"]}, "patch": {"description": "Updates an access binding on an account or property.", "flatPath": "v1alpha/properties/{propertiesId}/accessBindings/{accessBindingsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.accessBindings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this binding. Format: accounts/{account}/accessBindings/{access_binding} or properties/{property}/accessBindings/{access_binding} Example: \"accounts/100/accessBindings/200\"", "location": "path", "pattern": "^properties/[^/]+/accessBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "scopes": ["https://www.googleapis.com/auth/analytics.manage.users"]}}}, "adSenseLinks": {"methods": {"create": {"description": "Creates an AdSenseLink.", "flatPath": "v1alpha/properties/{propertiesId}/adSenseLinks", "httpMethod": "POST", "id": "analyticsadmin.properties.adSenseLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The property for which to create an AdSense Link. Format: properties/{propertyId} Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/adSenseLinks", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAdSenseLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAdSenseLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes an AdSenseLink.", "flatPath": "v1alpha/properties/{propertiesId}/adSenseLinks/{adSenseLinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.adSenseLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Unique identifier for the AdSense Link to be deleted. Format: properties/{propertyId}/adSenseLinks/{linkId} Example: properties/1234/adSenseLinks/5678", "location": "path", "pattern": "^properties/[^/]+/adSenseLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Looks up a single AdSenseLink.", "flatPath": "v1alpha/properties/{propertiesId}/adSenseLinks/{adSenseLinksId}", "httpMethod": "GET", "id": "analyticsadmin.properties.adSenseLinks.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Unique identifier for the AdSense Link requested. Format: properties/{propertyId}/adSenseLinks/{linkId} Example: properties/1234/adSenseLinks/5678", "location": "path", "pattern": "^properties/[^/]+/adSenseLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaAdSenseLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists AdSenseLinks on a property.", "flatPath": "v1alpha/properties/{propertiesId}/adSenseLinks", "httpMethod": "GET", "id": "analyticsadmin.properties.adSenseLinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from a previous `ListAdSenseLinks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAdSenseLinks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the parent property. Format: properties/{propertyId} Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/adSenseLinks", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListAdSenseLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}}}, "audiences": {"methods": {"archive": {"description": "Archives an Audience on a property.", "flatPath": "v1alpha/properties/{propertiesId}/audiences/{audiencesId}:archive", "httpMethod": "POST", "id": "analyticsadmin.properties.audiences.archive", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Example format: properties/1234/audiences/5678", "location": "path", "pattern": "^properties/[^/]+/audiences/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:archive", "request": {"$ref": "GoogleAnalyticsAdminV1alphaArchiveAudienceRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "create": {"description": "Creates an Audience.", "flatPath": "v1alpha/properties/{propertiesId}/audiences", "httpMethod": "POST", "id": "analyticsadmin.properties.audiences.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/audiences", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAudience"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAudience"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single Audience. Audiences created before 2020 may not be supported. Default audiences will not show filter definitions.", "flatPath": "v1alpha/properties/{propertiesId}/audiences/{audiencesId}", "httpMethod": "GET", "id": "analyticsadmin.properties.audiences.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Audience to get. Example format: properties/1234/audiences/5678", "location": "path", "pattern": "^properties/[^/]+/audiences/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaAudience"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists Audiences on a property. Audiences created before 2020 may not be supported. Default audiences will not show filter definitions.", "flatPath": "v1alpha/properties/{propertiesId}/audiences", "httpMethod": "GET", "id": "analyticsadmin.properties.audiences.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAudiences` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAudiences` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/audiences", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListAudiencesResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates an Audience on a property.", "flatPath": "v1alpha/properties/{propertiesId}/audiences/{audiencesId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.audiences.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name for this Audience resource. Format: properties/{propertyId}/audiences/{audienceId}", "location": "path", "pattern": "^properties/[^/]+/audiences/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaAudience"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaAudience"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "bigQueryLinks": {"methods": {"create": {"description": "Creates a BigQueryLink.", "flatPath": "v1alpha/properties/{propertiesId}/bigQueryLinks", "httpMethod": "POST", "id": "analyticsadmin.properties.bigQueryLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/bigQueryLinks", "request": {"$ref": "GoogleAnalyticsAdminV1alphaBigQueryLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaBigQueryLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a BigQueryLink on a property.", "flatPath": "v1alpha/properties/{propertiesId}/bigQueryLinks/{bigQueryLinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.bigQueryLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The BigQueryLink to delete. Example format: properties/1234/bigQueryLinks/5678", "location": "path", "pattern": "^properties/[^/]+/bigQueryLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single BigQuery Link.", "flatPath": "v1alpha/properties/{propertiesId}/bigQueryLinks/{bigQueryLinksId}", "httpMethod": "GET", "id": "analyticsadmin.properties.bigQueryLinks.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the BigQuery link to lookup. Format: properties/{property_id}/bigQueryLinks/{bigquery_link_id} Example: properties/123/bigQueryLinks/456", "location": "path", "pattern": "^properties/[^/]+/bigQueryLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaBigQueryLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists BigQuery Links on a property.", "flatPath": "v1alpha/properties/{propertiesId}/bigQueryLinks", "httpMethod": "GET", "id": "analyticsadmin.properties.bigQueryLinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListBigQueryLinks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListBigQueryLinks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the property to list BigQuery links under. Format: properties/{property_id} Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/bigQueryLinks", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListBigQueryLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a BigQueryLink.", "flatPath": "v1alpha/properties/{propertiesId}/bigQueryLinks/{bigQueryLinksId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.bigQueryLinks.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this BigQuery link. Format: 'properties/{property_id}/bigQueryLinks/{bigquery_link_id}' Format: 'properties/1234/bigQueryLinks/abc567'", "location": "path", "pattern": "^properties/[^/]+/bigQueryLinks/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaBigQueryLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaBigQueryLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "calculatedMetrics": {"methods": {"create": {"description": "Creates a CalculatedMetric.", "flatPath": "v1alpha/properties/{propertiesId}/calculatedMetrics", "httpMethod": "POST", "id": "analyticsadmin.properties.calculatedMetrics.create", "parameterOrder": ["parent"], "parameters": {"calculatedMetricId": {"description": "Required. The ID to use for the calculated metric which will become the final component of the calculated metric's resource name. This value should be 1-80 characters and valid characters are /[a-zA-Z0-9_]/, no spaces allowed. calculated_metric_id must be unique between all calculated metrics under a property. The calculated_metric_id is used when referencing this calculated metric from external APIs, for example, \"calcMetric:{calculated_metric_id}\".", "location": "query", "type": "string"}, "parent": {"description": "Required. Format: properties/{property_id} Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/calculatedMetrics", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCalculatedMetric"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaCalculatedMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a CalculatedMetric on a property.", "flatPath": "v1alpha/properties/{propertiesId}/calculatedMetrics/{calculatedMetricsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.calculatedMetrics.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CalculatedMetric to delete. Format: properties/{property_id}/calculatedMetrics/{calculated_metric_id} Example: properties/1234/calculatedMetrics/Metric01", "location": "path", "pattern": "^properties/[^/]+/calculatedMetrics/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single CalculatedMetric.", "flatPath": "v1alpha/properties/{propertiesId}/calculatedMetrics/{calculatedMetricsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.calculatedMetrics.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CalculatedMetric to get. Format: properties/{property_id}/calculatedMetrics/{calculated_metric_id} Example: properties/1234/calculatedMetrics/Metric01", "location": "path", "pattern": "^properties/[^/]+/calculatedMetrics/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaCalculatedMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists CalculatedMetrics on a property.", "flatPath": "v1alpha/properties/{propertiesId}/calculatedMetrics", "httpMethod": "GET", "id": "analyticsadmin.properties.calculatedMetrics.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListCalculatedMetrics` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListCalculatedMetrics` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/calculatedMetrics", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListCalculatedMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a CalculatedMetric on a property.", "flatPath": "v1alpha/properties/{propertiesId}/calculatedMetrics/{calculatedMetricsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.calculatedMetrics.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name for this CalculatedMetric. Format: 'properties/{property_id}/calculatedMetrics/{calculated_metric_id}'", "location": "path", "pattern": "^properties/[^/]+/calculatedMetrics/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCalculatedMetric"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaCalculatedMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "channelGroups": {"methods": {"create": {"description": "Creates a ChannelGroup.", "flatPath": "v1alpha/properties/{propertiesId}/channelGroups", "httpMethod": "POST", "id": "analyticsadmin.properties.channelGroups.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The property for which to create a ChannelGroup. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/channelGroups", "request": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroup"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroup"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a ChannelGroup on a property.", "flatPath": "v1alpha/properties/{propertiesId}/channelGroups/{channelGroupsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.channelGroups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The ChannelGroup to delete. Example format: properties/1234/channelGroups/5678", "location": "path", "pattern": "^properties/[^/]+/channelGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single ChannelGroup.", "flatPath": "v1alpha/properties/{propertiesId}/channelGroups/{channelGroupsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.channelGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The ChannelGroup to get. Example format: properties/1234/channelGroups/5678", "location": "path", "pattern": "^properties/[^/]+/channelGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroup"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists ChannelGroups on a property.", "flatPath": "v1alpha/properties/{propertiesId}/channelGroups", "httpMethod": "GET", "id": "analyticsadmin.properties.channelGroups.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListChannelGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListChannelGroups` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The property for which to list ChannelGroups. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/channelGroups", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListChannelGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a ChannelGroup.", "flatPath": "v1alpha/properties/{propertiesId}/channelGroups/{channelGroupsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.channelGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name for this Channel Group resource. Format: properties/{property}/channelGroups/{channel_group}", "location": "path", "pattern": "^properties/[^/]+/channelGroups/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroup"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroup"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "conversionEvents": {"deprecated": true, "methods": {"create": {"deprecated": true, "description": "Deprecated: Use `CreateKeyEvent` instead. Creates a conversion event with the specified attributes.", "flatPath": "v1alpha/properties/{propertiesId}/conversionEvents", "httpMethod": "POST", "id": "analyticsadmin.properties.conversionEvents.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the parent property where this conversion event will be created. Format: properties/123", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/conversionEvents", "request": {"$ref": "GoogleAnalyticsAdminV1alphaConversionEvent"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaConversionEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"deprecated": true, "description": "Deprecated: Use `DeleteKeyEvent` instead. Deletes a conversion event in a property.", "flatPath": "v1alpha/properties/{propertiesId}/conversionEvents/{conversionEventsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.conversionEvents.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the conversion event to delete. Format: properties/{property}/conversionEvents/{conversion_event} Example: \"properties/123/conversionEvents/456\"", "location": "path", "pattern": "^properties/[^/]+/conversionEvents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"deprecated": true, "description": "Deprecated: Use `GetKeyEvent` instead. Retrieve a single conversion event.", "flatPath": "v1alpha/properties/{propertiesId}/conversionEvents/{conversionEventsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.conversionEvents.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the conversion event to retrieve. Format: properties/{property}/conversionEvents/{conversion_event} Example: \"properties/123/conversionEvents/456\"", "location": "path", "pattern": "^properties/[^/]+/conversionEvents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaConversionEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"deprecated": true, "description": "Deprecated: Use `ListKeyEvents` instead. Returns a list of conversion events in the specified parent property. Returns an empty list if no conversion events are found.", "flatPath": "v1alpha/properties/{propertiesId}/conversionEvents", "httpMethod": "GET", "id": "analyticsadmin.properties.conversionEvents.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListConversionEvents` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListConversionEvents` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent property. Example: 'properties/123'", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/conversionEvents", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListConversionEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"deprecated": true, "description": "Deprecated: Use `UpdateKeyEvent` instead. Updates a conversion event with the specified attributes.", "flatPath": "v1alpha/properties/{propertiesId}/conversionEvents/{conversionEventsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.conversionEvents.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this conversion event. Format: properties/{property}/conversionEvents/{conversion_event}", "location": "path", "pattern": "^properties/[^/]+/conversionEvents/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaConversionEvent"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaConversionEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "customDimensions": {"methods": {"archive": {"description": "Archives a CustomDimension on a property.", "flatPath": "v1alpha/properties/{propertiesId}/customDimensions/{customDimensionsId}:archive", "httpMethod": "POST", "id": "analyticsadmin.properties.customDimensions.archive", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CustomDimension to archive. Example format: properties/1234/customDimensions/5678", "location": "path", "pattern": "^properties/[^/]+/customDimensions/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:archive", "request": {"$ref": "GoogleAnalyticsAdminV1alphaArchiveCustomDimensionRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "create": {"description": "Creates a CustomDimension.", "flatPath": "v1alpha/properties/{propertiesId}/customDimensions", "httpMethod": "POST", "id": "analyticsadmin.properties.customDimensions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/customDimensions", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCustomDimension"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaCustomDimension"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single CustomDimension.", "flatPath": "v1alpha/properties/{propertiesId}/customDimensions/{customDimensionsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.customDimensions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CustomDimension to get. Example format: properties/1234/customDimensions/5678", "location": "path", "pattern": "^properties/[^/]+/customDimensions/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaCustomDimension"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists CustomDimensions on a property.", "flatPath": "v1alpha/properties/{propertiesId}/customDimensions", "httpMethod": "GET", "id": "analyticsadmin.properties.customDimensions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListCustomDimensions` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListCustomDimensions` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/customDimensions", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListCustomDimensionsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a CustomDimension on a property.", "flatPath": "v1alpha/properties/{propertiesId}/customDimensions/{customDimensionsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.customDimensions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name for this CustomDimension resource. Format: properties/{property}/customDimensions/{customDimension}", "location": "path", "pattern": "^properties/[^/]+/customDimensions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCustomDimension"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaCustomDimension"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "customMetrics": {"methods": {"archive": {"description": "Archives a CustomMetric on a property.", "flatPath": "v1alpha/properties/{propertiesId}/customMetrics/{customMetricsId}:archive", "httpMethod": "POST", "id": "analyticsadmin.properties.customMetrics.archive", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CustomMetric to archive. Example format: properties/1234/customMetrics/5678", "location": "path", "pattern": "^properties/[^/]+/customMetrics/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:archive", "request": {"$ref": "GoogleAnalyticsAdminV1alphaArchiveCustomMetricRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "create": {"description": "Creates a CustomMetric.", "flatPath": "v1alpha/properties/{propertiesId}/customMetrics", "httpMethod": "POST", "id": "analyticsadmin.properties.customMetrics.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/customMetrics", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCustomMetric"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaCustomMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single CustomMetric.", "flatPath": "v1alpha/properties/{propertiesId}/customMetrics/{customMetricsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.customMetrics.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CustomMetric to get. Example format: properties/1234/customMetrics/5678", "location": "path", "pattern": "^properties/[^/]+/customMetrics/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaCustomMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists CustomMetrics on a property.", "flatPath": "v1alpha/properties/{propertiesId}/customMetrics", "httpMethod": "GET", "id": "analyticsadmin.properties.customMetrics.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListCustomMetrics` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListCustomMetrics` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/customMetrics", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListCustomMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a CustomMetric on a property.", "flatPath": "v1alpha/properties/{propertiesId}/customMetrics/{customMetricsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.customMetrics.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name for this CustomMetric resource. Format: properties/{property}/customMetrics/{customMetric}", "location": "path", "pattern": "^properties/[^/]+/customMetrics/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCustomMetric"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaCustomMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "dataStreams": {"methods": {"create": {"description": "Creates a DataStream.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams", "httpMethod": "POST", "id": "analyticsadmin.properties.dataStreams.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/dataStreams", "request": {"$ref": "GoogleAnalyticsAdminV1alphaDataStream"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaDataStream"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a DataStream on a property.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.dataStreams.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DataStream to delete. Example format: properties/1234/dataStreams/5678", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single DataStream.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DataStream to get. Example format: properties/1234/dataStreams/5678", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaDataStream"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getDataRedactionSettings": {"description": "Lookup for a single DataRedactionSettings.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/dataRedactionSettings", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.getDataRedactionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the settings to lookup. Format: properties/{property}/dataStreams/{data_stream}/dataRedactionSettings Example: \"properties/1000/dataStreams/2000/dataRedactionSettings\"", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/dataRedactionSettings$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaDataRedactionSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getEnhancedMeasurementSettings": {"description": "Returns the enhanced measurement settings for this data stream. Note that the stream must enable enhanced measurement for these settings to take effect.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/enhancedMeasurementSettings", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.getEnhancedMeasurementSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the settings to lookup. Format: properties/{property}/dataStreams/{data_stream}/enhancedMeasurementSettings Example: \"properties/1000/dataStreams/2000/enhancedMeasurementSettings\"", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/enhancedMeasurementSettings$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaEnhancedMeasurementSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getGlobalSiteTag": {"description": "Returns the Site Tag for the specified web stream. Site Tags are immutable singletons.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/globalSiteTag", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.getGlobalSiteTag", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the site tag to lookup. Note that site tags are singletons and do not have unique IDs. Format: properties/{property_id}/dataStreams/{stream_id}/globalSiteTag Example: `properties/123/dataStreams/456/globalSiteTag`", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/globalSiteTag$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaGlobalSiteTag"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists DataStreams on a property.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListDataStreams` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDataStreams` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/dataStreams", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListDataStreamsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a DataStream on a property.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this Data Stream. Format: properties/{property_id}/dataStreams/{stream_id} Example: \"properties/1000/dataStreams/2000\"", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaDataStream"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaDataStream"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "updateDataRedactionSettings": {"description": "Updates a DataRedactionSettings on a property.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/dataRedactionSettings", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.updateDataRedactionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Name of this Data Redaction Settings resource. Format: properties/{property_id}/dataStreams/{data_stream}/dataRedactionSettings Example: \"properties/1000/dataStreams/2000/dataRedactionSettings\"", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/dataRedactionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaDataRedactionSettings"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaDataRedactionSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "updateEnhancedMeasurementSettings": {"description": "Updates the enhanced measurement settings for this data stream. Note that the stream must enable enhanced measurement for these settings to take effect.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/enhancedMeasurementSettings", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.updateEnhancedMeasurementSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of the Enhanced Measurement Settings. Format: properties/{property_id}/dataStreams/{data_stream}/enhancedMeasurementSettings Example: \"properties/1000/dataStreams/2000/enhancedMeasurementSettings\"", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/enhancedMeasurementSettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaEnhancedMeasurementSettings"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaEnhancedMeasurementSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}, "resources": {"eventCreateRules": {"methods": {"create": {"description": "Creates an EventCreateRule.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventCreateRules", "httpMethod": "POST", "id": "analyticsadmin.properties.dataStreams.eventCreateRules.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/123/dataStreams/456", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/eventCreateRules", "request": {"$ref": "GoogleAnalyticsAdminV1alphaEventCreateRule"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaEventCreateRule"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes an EventCreateRule.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventCreateRules/{eventCreateRulesId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.dataStreams.eventCreateRules.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Example format: properties/123/dataStreams/456/eventCreateRules/789", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/eventCreateRules/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single EventCreateRule.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventCreateRules/{eventCreateRulesId}", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.eventCreateRules.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the EventCreateRule to get. Example format: properties/123/dataStreams/456/eventCreateRules/789", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/eventCreateRules/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaEventCreateRule"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists EventCreateRules on a web data stream.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventCreateRules", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.eventCreateRules.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListEventCreateRules` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListEventCreateRules` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/123/dataStreams/456", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/eventCreateRules", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListEventCreateRulesResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates an EventCreateRule.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventCreateRules/{eventCreateRulesId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.eventCreateRules.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name for this EventCreateRule resource. Format: properties/{property}/dataStreams/{data_stream}/eventCreateRules/{event_create_rule}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/eventCreateRules/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaEventCreateRule"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaEventCreateRule"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "eventEditRules": {"methods": {"create": {"description": "Creates an EventEditRule.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventEditRules", "httpMethod": "POST", "id": "analyticsadmin.properties.dataStreams.eventEditRules.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/123/dataStreams/456", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/eventEditRules", "request": {"$ref": "GoogleAnalyticsAdminV1alphaEventEditRule"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaEventEditRule"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes an EventEditRule.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventEditRules/{eventEditRulesId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.dataStreams.eventEditRules.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Example format: properties/123/dataStreams/456/eventEditRules/789", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/eventEditRules/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single EventEditRule.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventEditRules/{eventEditRulesId}", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.eventEditRules.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the EventEditRule to get. Example format: properties/123/dataStreams/456/eventEditRules/789", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/eventEditRules/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaEventEditRule"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists EventEditRules on a web data stream.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventEditRules", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.eventEditRules.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListEventEditRules` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListEventEditRules` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/123/dataStreams/456", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/eventEditRules", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListEventEditRulesResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates an EventEditRule.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventEditRules/{eventEditRulesId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.eventEditRules.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Resource name for this EventEditRule resource. Format: properties/{property}/dataStreams/{data_stream}/eventEditRules/{event_edit_rule}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/eventEditRules/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaEventEditRule"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaEventEditRule"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "reorder": {"description": "Changes the processing order of event edit rules on the specified stream.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/eventEditRules:reorder", "httpMethod": "POST", "id": "analyticsadmin.properties.dataStreams.eventEditRules.reorder", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/123/dataStreams/456", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/eventEditRules:reorder", "request": {"$ref": "GoogleAnalyticsAdminV1alphaReorderEventEditRulesRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "measurementProtocolSecrets": {"methods": {"create": {"description": "Creates a measurement protocol secret.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets", "httpMethod": "POST", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this secret will be created. Format: properties/{property}/dataStreams/{dataStream}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/measurementProtocolSecrets", "request": {"$ref": "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes target MeasurementProtocolSecret.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets/{measurementProtocolSecretsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the MeasurementProtocolSecret to delete. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/measurementProtocolSecrets/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single MeasurementProtocolSecret.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets/{measurementProtocolSecretsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the measurement protocol secret to lookup. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/measurementProtocolSecrets/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Returns child MeasurementProtocolSecrets under the specified parent Property.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 10 resources will be returned. The maximum value is 10. Higher values will be coerced to the maximum.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListMeasurementProtocolSecrets` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMeasurementProtocolSecrets` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent stream. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/measurementProtocolSecrets", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListMeasurementProtocolSecretsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a measurement protocol secret.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets/{measurementProtocolSecretsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this secret. This secret may be a child of any type of stream. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/measurementProtocolSecrets/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "sKAdNetworkConversionValueSchema": {"methods": {"create": {"description": "Creates a SKAdNetworkConversionValueSchema.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/sKAdNetworkConversionValueSchema", "httpMethod": "POST", "id": "analyticsadmin.properties.dataStreams.sKAdNetworkConversionValueSchema.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this schema will be created. Format: properties/{property}/dataStreams/{dataStream}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/sKAdNetworkConversionValueSchema", "request": {"$ref": "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes target SKAdNetworkConversionValueSchema.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/sKAdNetworkConversionValueSchema/{sKAdNetworkConversionValueSchemaId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.dataStreams.sKAdNetworkConversionValueSchema.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SKAdNetworkConversionValueSchema to delete. Format: properties/{property}/dataStreams/{dataStream}/sKAdNetworkConversionValueSchema/{skadnetwork_conversion_value_schema}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/sKAdNetworkConversionValueSchema/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Looks up a single SKAdNetworkConversionValueSchema.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/sKAdNetworkConversionValueSchema/{sKAdNetworkConversionValueSchemaId}", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.sKAdNetworkConversionValueSchema.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of SKAdNetwork conversion value schema to look up. Format: properties/{property}/dataStreams/{dataStream}/sKAdNetworkConversionValueSchema/{skadnetwork_conversion_value_schema}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/sKAdNetworkConversionValueSchema/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists SKAdNetworkConversionValueSchema on a stream. Properties can have at most one SKAdNetworkConversionValueSchema.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/sKAdNetworkConversionValueSchema", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.sKAdNetworkConversionValueSchema.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListSKAdNetworkConversionValueSchemas` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSKAdNetworkConversionValueSchema` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The DataStream resource to list schemas for. Format: properties/{property_id}/dataStreams/{dataStream} Example: properties/1234/dataStreams/5678", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/sKAdNetworkConversionValueSchema", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListSKAdNetworkConversionValueSchemasResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a SKAdNetworkConversionValueSchema.", "flatPath": "v1alpha/properties/{propertiesId}/dataStreams/{dataStreamsId}/sKAdNetworkConversionValueSchema/{sKAdNetworkConversionValueSchemaId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.sKAdNetworkConversionValueSchema.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of the schema. This will be child of ONLY an iOS stream, and there can be at most one such child under an iOS stream. Format: properties/{property}/dataStreams/{dataStream}/sKAdNetworkConversionValueSchema", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/sKAdNetworkConversionValueSchema/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}}}, "displayVideo360AdvertiserLinkProposals": {"methods": {"approve": {"description": "Approves a DisplayVideo360AdvertiserLinkProposal. The DisplayVideo360AdvertiserLinkProposal will be deleted and a new DisplayVideo360AdvertiserLink will be created.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinkProposals/{displayVideo360AdvertiserLinkProposalsId}:approve", "httpMethod": "POST", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinkProposals.approve", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DisplayVideo360AdvertiserLinkProposal to approve. Example format: properties/1234/displayVideo360AdvertiserLinkProposals/5678", "location": "path", "pattern": "^properties/[^/]+/displayVideo360AdvertiserLinkProposals/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:approve", "request": {"$ref": "GoogleAnalyticsAdminV1alphaApproveDisplayVideo360AdvertiserLinkProposalRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaApproveDisplayVideo360AdvertiserLinkProposalResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "cancel": {"description": "Cancels a DisplayVideo360AdvertiserLinkProposal. Cancelling can mean either: - Declining a proposal initiated from Display & Video 360 - Withdrawing a proposal initiated from Google Analytics After being cancelled, a proposal will eventually be deleted automatically.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinkProposals/{displayVideo360AdvertiserLinkProposalsId}:cancel", "httpMethod": "POST", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinkProposals.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DisplayVideo360AdvertiserLinkProposal to cancel. Example format: properties/1234/displayVideo360AdvertiserLinkProposals/5678", "location": "path", "pattern": "^properties/[^/]+/displayVideo360AdvertiserLinkProposals/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:cancel", "request": {"$ref": "GoogleAnalyticsAdminV1alphaCancelDisplayVideo360AdvertiserLinkProposalRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLinkProposal"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "create": {"description": "Creates a DisplayVideo360AdvertiserLinkProposal.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinkProposals", "httpMethod": "POST", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinkProposals.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/displayVideo360AdvertiserLinkProposals", "request": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLinkProposal"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLinkProposal"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a DisplayVideo360AdvertiserLinkProposal on a property. This can only be used on cancelled proposals.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinkProposals/{displayVideo360AdvertiserLinkProposalsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinkProposals.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DisplayVideo360AdvertiserLinkProposal to delete. Example format: properties/1234/displayVideo360AdvertiserLinkProposals/5678", "location": "path", "pattern": "^properties/[^/]+/displayVideo360AdvertiserLinkProposals/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single DisplayVideo360AdvertiserLinkProposal.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinkProposals/{displayVideo360AdvertiserLinkProposalsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinkProposals.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DisplayVideo360AdvertiserLinkProposal to get. Example format: properties/1234/displayVideo360AdvertiserLinkProposals/5678", "location": "path", "pattern": "^properties/[^/]+/displayVideo360AdvertiserLinkProposals/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLinkProposal"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists DisplayVideo360AdvertiserLinkProposals on a property.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinkProposals", "httpMethod": "GET", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinkProposals.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListDisplayVideo360AdvertiserLinkProposals` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDisplayVideo360AdvertiserLinkProposals` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/displayVideo360AdvertiserLinkProposals", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListDisplayVideo360AdvertiserLinkProposalsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}}}, "displayVideo360AdvertiserLinks": {"methods": {"create": {"description": "Creates a DisplayVideo360AdvertiserLink. This can only be utilized by users who have proper authorization both on the Google Analytics property and on the Display & Video 360 advertiser. Users who do not have access to the Display & Video 360 advertiser should instead seek to create a DisplayVideo360LinkProposal.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinks", "httpMethod": "POST", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/displayVideo360AdvertiserLinks", "request": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a DisplayVideo360AdvertiserLink on a property.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinks/{displayVideo360AdvertiserLinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DisplayVideo360AdvertiserLink to delete. Example format: properties/1234/displayVideo360AdvertiserLinks/5678", "location": "path", "pattern": "^properties/[^/]+/displayVideo360AdvertiserLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Look up a single DisplayVideo360AdvertiserLink", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinks/{displayVideo360AdvertiserLinksId}", "httpMethod": "GET", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinks.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DisplayVideo360AdvertiserLink to get. Example format: properties/1234/displayVideo360AdvertiserLink/5678", "location": "path", "pattern": "^properties/[^/]+/displayVideo360AdvertiserLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists all DisplayVideo360AdvertiserLinks on a property.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinks", "httpMethod": "GET", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListDisplayVideo360AdvertiserLinks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDisplayVideo360AdvertiserLinks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/displayVideo360AdvertiserLinks", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListDisplayVideo360AdvertiserLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a DisplayVideo360AdvertiserLink on a property.", "flatPath": "v1alpha/properties/{propertiesId}/displayVideo360AdvertiserLinks/{displayVideo360AdvertiserLinksId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.displayVideo360AdvertiserLinks.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name for this DisplayVideo360AdvertiserLink resource. Format: properties/{propertyId}/displayVideo360AdvertiserLinks/{linkId} Note: linkId is not the Display & Video 360 Advertiser ID", "location": "path", "pattern": "^properties/[^/]+/displayVideo360AdvertiserLinks/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "expandedDataSets": {"methods": {"create": {"description": "Creates a ExpandedDataSet.", "flatPath": "v1alpha/properties/{propertiesId}/expandedDataSets", "httpMethod": "POST", "id": "analyticsadmin.properties.expandedDataSets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/expandedDataSets", "request": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSet"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSet"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a ExpandedDataSet on a property.", "flatPath": "v1alpha/properties/{propertiesId}/expandedDataSets/{expandedDataSetsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.expandedDataSets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Example format: properties/1234/expandedDataSets/5678", "location": "path", "pattern": "^properties/[^/]+/expandedDataSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single ExpandedDataSet.", "flatPath": "v1alpha/properties/{propertiesId}/expandedDataSets/{expandedDataSetsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.expandedDataSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ExpandedDataSet to get. Example format: properties/1234/expandedDataSets/5678", "location": "path", "pattern": "^properties/[^/]+/expandedDataSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSet"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists ExpandedDataSets on a property.", "flatPath": "v1alpha/properties/{propertiesId}/expandedDataSets", "httpMethod": "GET", "id": "analyticsadmin.properties.expandedDataSets.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListExpandedDataSets` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListExpandedDataSet` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/expandedDataSets", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListExpandedDataSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a ExpandedDataSet on a property.", "flatPath": "v1alpha/properties/{propertiesId}/expandedDataSets/{expandedDataSetsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.expandedDataSets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name for this ExpandedDataSet resource. Format: properties/{property_id}/expandedDataSets/{expanded_data_set}", "location": "path", "pattern": "^properties/[^/]+/expandedDataSets/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSet"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSet"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "firebaseLinks": {"methods": {"create": {"description": "Creates a FirebaseLink. Properties can have at most one FirebaseLink.", "flatPath": "v1alpha/properties/{propertiesId}/firebaseLinks", "httpMethod": "POST", "id": "analyticsadmin.properties.firebaseLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Format: properties/{property_id} Example: `properties/1234`", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/firebaseLinks", "request": {"$ref": "GoogleAnalyticsAdminV1alphaFirebaseLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaFirebaseLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a FirebaseLink on a property", "flatPath": "v1alpha/properties/{propertiesId}/firebaseLinks/{firebaseLinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.firebaseLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Format: properties/{property_id}/firebaseLinks/{firebase_link_id} Example: `properties/1234/firebaseLinks/5678`", "location": "path", "pattern": "^properties/[^/]+/firebaseLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "list": {"description": "Lists FirebaseLinks on a property. Properties can have at most one FirebaseLink.", "flatPath": "v1alpha/properties/{propertiesId}/firebaseLinks", "httpMethod": "GET", "id": "analyticsadmin.properties.firebaseLinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListFirebaseLinks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListFirebaseLinks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Format: properties/{property_id} Example: `properties/1234`", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/firebaseLinks", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListFirebaseLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}}}, "googleAdsLinks": {"methods": {"create": {"description": "Creates a GoogleAdsLink.", "flatPath": "v1alpha/properties/{propertiesId}/googleAdsLinks", "httpMethod": "POST", "id": "analyticsadmin.properties.googleAdsLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/googleAdsLinks", "request": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleAdsLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleAdsLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a GoogleAdsLink on a property", "flatPath": "v1alpha/properties/{propertiesId}/googleAdsLinks/{googleAdsLinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.googleAdsLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Example format: properties/1234/googleAdsLinks/5678", "location": "path", "pattern": "^properties/[^/]+/googleAdsLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "list": {"description": "Lists GoogleAdsLinks on a property.", "flatPath": "v1alpha/properties/{propertiesId}/googleAdsLinks", "httpMethod": "GET", "id": "analyticsadmin.properties.googleAdsLinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListGoogleAdsLinks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListGoogleAdsLinks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/googleAdsLinks", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListGoogleAdsLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a GoogleAdsLink on a property", "flatPath": "v1alpha/properties/{propertiesId}/googleAdsLinks/{googleAdsLinksId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.googleAdsLinks.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Format: properties/{propertyId}/googleAdsLinks/{googleAdsLinkId} Note: googleAdsLinkId is not the Google Ads customer ID.", "location": "path", "pattern": "^properties/[^/]+/googleAdsLinks/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleAdsLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleAdsLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "keyEvents": {"methods": {"create": {"description": "Creates a Key Event.", "flatPath": "v1alpha/properties/{propertiesId}/keyEvents", "httpMethod": "POST", "id": "analyticsadmin.properties.keyEvents.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the parent property where this Key Event will be created. Format: properties/123", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/keyEvents", "request": {"$ref": "GoogleAnalyticsAdminV1alphaKeyEvent"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaKeyEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a Key Event.", "flatPath": "v1alpha/properties/{propertiesId}/keyEvents/{keyEventsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.keyEvents.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Key Event to delete. Format: properties/{property}/keyEvents/{key_event} Example: \"properties/123/keyEvents/456\"", "location": "path", "pattern": "^properties/[^/]+/keyEvents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Retrieve a single Key Event.", "flatPath": "v1alpha/properties/{propertiesId}/keyEvents/{keyEventsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.keyEvents.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Key Event to retrieve. Format: properties/{property}/keyEvents/{key_event} Example: \"properties/123/keyEvents/456\"", "location": "path", "pattern": "^properties/[^/]+/keyEvents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaKeyEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Returns a list of Key Events in the specified parent property. Returns an empty list if no Key Events are found.", "flatPath": "v1alpha/properties/{propertiesId}/keyEvents", "httpMethod": "GET", "id": "analyticsadmin.properties.keyEvents.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListKeyEvents` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListKeyEvents` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent property. Example: 'properties/123'", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/keyEvents", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListKeyEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a Key Event.", "flatPath": "v1alpha/properties/{propertiesId}/keyEvents/{keyEventsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.keyEvents.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this key event. Format: properties/{property}/keyEvents/{key_event}", "location": "path", "pattern": "^properties/[^/]+/keyEvents/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaKeyEvent"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaKeyEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "reportingDataAnnotations": {"methods": {"create": {"description": "Creates a Reporting Data Annotation.", "flatPath": "v1alpha/properties/{propertiesId}/reportingDataAnnotations", "httpMethod": "POST", "id": "analyticsadmin.properties.reportingDataAnnotations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The property for which to create a Reporting Data Annotation. Format: properties/property_id Example: properties/123", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/reportingDataAnnotations", "request": {"$ref": "GoogleAnalyticsAdminV1alphaReportingDataAnnotation"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaReportingDataAnnotation"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a Reporting Data Annotation.", "flatPath": "v1alpha/properties/{propertiesId}/reportingDataAnnotations/{reportingDataAnnotationsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.reportingDataAnnotations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Reporting Data Annotation to delete. Format: properties/property_id/reportingDataAnnotations/reporting_data_annotation Example: properties/123/reportingDataAnnotations/456", "location": "path", "pattern": "^properties/[^/]+/reportingDataAnnotations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup a single Reporting Data Annotation.", "flatPath": "v1alpha/properties/{propertiesId}/reportingDataAnnotations/{reportingDataAnnotationsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.reportingDataAnnotations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Reporting Data Annotation to lookup. Format: properties/property_id/reportingDataAnnotations/reportingDataAnnotation Example: properties/123/reportingDataAnnotations/456", "location": "path", "pattern": "^properties/[^/]+/reportingDataAnnotations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaReportingDataAnnotation"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "List all Reporting Data Annotations on a property.", "flatPath": "v1alpha/properties/{propertiesId}/reportingDataAnnotations", "httpMethod": "GET", "id": "analyticsadmin.properties.reportingDataAnnotations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter that restricts which reporting data annotations under the parent property are listed. Supported fields are: * 'name' * `title` * `description` * `annotation_date` * `annotation_date_range` * `color` Additionally, this API provides the following helper functions: * annotation_duration() : the duration that this annotation marks, [durations](https://github.com/protocolbuffers/protobuf/blob/main/src/google/protobuf/duration.proto). expect a numeric representation of seconds followed by an `s` suffix. * is_annotation_in_range(start_date, end_date) : if the annotation is in the range specified by the `start_date` and `end_date`. The dates are in ISO-8601 format, for example `2031-06-28`. Supported operations: * `=` : equals * `!=` : not equals * `<` : less than * `>` : greater than * `<=` : less than or equals * `>=` : greater than or equals * `:` : has operator * `=~` : [regular expression](https://github.com/google/re2/wiki/Syntax) match * `!~` : [regular expression](https://github.com/google/re2/wiki/Syntax) does not match * `NOT` : Logical not * `AND` : Logical and * `OR` : Logical or Examples: 1. `title=\"Holiday Sale\"` 2. `description=~\"[Bb]ig [Gg]ame.*[Ss]ale\"` 3. `is_annotation_in_range(\"2025-12-25\", \"2026-01-16\") = true` 4. `annotation_duration() >= 172800s AND title:BOGO`", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListReportingDataAnnotations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListReportingDataAnnotations` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the property. Format: properties/property_id Example: properties/123", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/reportingDataAnnotations", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListReportingDataAnnotationsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a Reporting Data Annotation.", "flatPath": "v1alpha/properties/{propertiesId}/reportingDataAnnotations/{reportingDataAnnotationsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.reportingDataAnnotations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Identifier. Resource name of this Reporting Data Annotation. Format: 'properties/{property_id}/reportingDataAnnotations/{reporting_data_annotation}' Format: 'properties/123/reportingDataAnnotations/456'", "location": "path", "pattern": "^properties/[^/]+/reportingDataAnnotations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Field names must be in snake case (for example, \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaReportingDataAnnotation"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaReportingDataAnnotation"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "rollupPropertySourceLinks": {"methods": {"create": {"description": "Creates a roll-up property source link. Only roll-up properties can have source links, so this method will throw an error if used on other types of properties.", "flatPath": "v1alpha/properties/{propertiesId}/rollupPropertySourceLinks", "httpMethod": "POST", "id": "analyticsadmin.properties.rollupPropertySourceLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Format: properties/{property_id} Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/rollupPropertySourceLinks", "request": {"$ref": "GoogleAnalyticsAdminV1alphaRollupPropertySourceLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaRollupPropertySourceLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a roll-up property source link. Only roll-up properties can have source links, so this method will throw an error if used on other types of properties.", "flatPath": "v1alpha/properties/{propertiesId}/rollupPropertySourceLinks/{rollupPropertySourceLinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.rollupPropertySourceLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Format: properties/{property_id}/rollupPropertySourceLinks/{rollup_property_source_link_id} Example: properties/1234/rollupPropertySourceLinks/5678", "location": "path", "pattern": "^properties/[^/]+/rollupPropertySourceLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single roll-up property source Link. Only roll-up properties can have source links, so this method will throw an error if used on other types of properties.", "flatPath": "v1alpha/properties/{propertiesId}/rollupPropertySourceLinks/{rollupPropertySourceLinksId}", "httpMethod": "GET", "id": "analyticsadmin.properties.rollupPropertySourceLinks.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the roll-up property source link to lookup. Format: properties/{property_id}/rollupPropertySourceLinks/{rollup_property_source_link_id} Example: properties/123/rollupPropertySourceLinks/456", "location": "path", "pattern": "^properties/[^/]+/rollupPropertySourceLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaRollupPropertySourceLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists roll-up property source Links on a property. Only roll-up properties can have source links, so this method will throw an error if used on other types of properties.", "flatPath": "v1alpha/properties/{propertiesId}/rollupPropertySourceLinks", "httpMethod": "GET", "id": "analyticsadmin.properties.rollupPropertySourceLinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListRollupPropertySourceLinks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListRollupPropertySourceLinks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the roll-up property to list roll-up property source links under. Format: properties/{property_id} Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/rollupPropertySourceLinks", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListRollupPropertySourceLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}}}, "searchAds360Links": {"methods": {"create": {"description": "Creates a SearchAds360Link.", "flatPath": "v1alpha/properties/{propertiesId}/searchAds360Links", "httpMethod": "POST", "id": "analyticsadmin.properties.searchAds360Links.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/searchAds360Links", "request": {"$ref": "GoogleAnalyticsAdminV1alphaSearchAds360Link"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaSearchAds360Link"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a SearchAds360Link on a property.", "flatPath": "v1alpha/properties/{propertiesId}/searchAds360Links/{searchAds360LinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.searchAds360Links.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SearchAds360Link to delete. Example format: properties/1234/SearchAds360Links/5678", "location": "path", "pattern": "^properties/[^/]+/searchAds360Links/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Look up a single SearchAds360Link", "flatPath": "v1alpha/properties/{propertiesId}/searchAds360Links/{searchAds360LinksId}", "httpMethod": "GET", "id": "analyticsadmin.properties.searchAds360Links.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SearchAds360Link to get. Example format: properties/1234/SearchAds360Link/5678", "location": "path", "pattern": "^properties/[^/]+/searchAds360Links/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaSearchAds360Link"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists all SearchAds360Links on a property.", "flatPath": "v1alpha/properties/{propertiesId}/searchAds360Links", "httpMethod": "GET", "id": "analyticsadmin.properties.searchAds360Links.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListSearchAds360Links` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSearchAds360Links` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/searchAds360Links", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListSearchAds360LinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a SearchAds360Link on a property.", "flatPath": "v1alpha/properties/{propertiesId}/searchAds360Links/{searchAds360LinksId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.searchAds360Links.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name for this SearchAds360Link resource. Format: properties/{propertyId}/searchAds360Links/{linkId} Note: linkId is not the Search Ads 360 advertiser ID", "location": "path", "pattern": "^properties/[^/]+/searchAds360Links/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaSearchAds360Link"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaSearchAds360Link"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "subpropertyEventFilters": {"methods": {"create": {"description": "Creates a subproperty Event Filter.", "flatPath": "v1alpha/properties/{propertiesId}/subpropertyEventFilters", "httpMethod": "POST", "id": "analyticsadmin.properties.subpropertyEventFilters.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The ordinary property for which to create a subproperty event filter. Format: properties/property_id Example: properties/123", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/subpropertyEventFilters", "request": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a subproperty event filter.", "flatPath": "v1alpha/properties/{propertiesId}/subpropertyEventFilters/{subpropertyEventFiltersId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.subpropertyEventFilters.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the subproperty event filter to delete. Format: properties/property_id/subpropertyEventFilters/subproperty_event_filter Example: properties/123/subpropertyEventFilters/456", "location": "path", "pattern": "^properties/[^/]+/subpropertyEventFilters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single subproperty Event Filter.", "flatPath": "v1alpha/properties/{propertiesId}/subpropertyEventFilters/{subpropertyEventFiltersId}", "httpMethod": "GET", "id": "analyticsadmin.properties.subpropertyEventFilters.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the subproperty event filter to lookup. Format: properties/property_id/subpropertyEventFilters/subproperty_event_filter Example: properties/123/subpropertyEventFilters/456", "location": "path", "pattern": "^properties/[^/]+/subpropertyEventFilters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "List all subproperty Event Filters on a property.", "flatPath": "v1alpha/properties/{propertiesId}/subpropertyEventFilters", "httpMethod": "GET", "id": "analyticsadmin.properties.subpropertyEventFilters.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListSubpropertyEventFilters` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSubpropertyEventFilters` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the ordinary property. Format: properties/property_id Example: properties/123", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/subpropertyEventFilters", "response": {"$ref": "GoogleAnalyticsAdminV1alphaListSubpropertyEventFiltersResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a subproperty Event Filter.", "flatPath": "v1alpha/properties/{propertiesId}/subpropertyEventFilters/{subpropertyEventFiltersId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.subpropertyEventFilters.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Format: properties/{ordinary_property_id}/subpropertyEventFilters/{sub_property_event_filter} Example: properties/1234/subpropertyEventFilters/5678", "location": "path", "pattern": "^properties/[^/]+/subpropertyEventFilters/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update. Field names must be in snake case (for example, \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter"}, "response": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}}}}, "revision": "20250329", "rootUrl": "https://analyticsadmin.googleapis.com/", "schemas": {"GoogleAnalyticsAdminV1alphaAccessBetweenFilter": {"description": "To express that the result needs to be between two numbers (inclusive).", "id": "GoogleAnalyticsAdminV1alphaAccessBetweenFilter", "properties": {"fromValue": {"$ref": "GoogleAnalyticsAdminV1alphaNumericValue", "description": "Begins with this number."}, "toValue": {"$ref": "GoogleAnalyticsAdminV1alphaNumericValue", "description": "Ends with this number."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessBinding": {"description": "A binding of a user to a set of roles.", "id": "GoogleAnalyticsAdminV1alphaAccessBinding", "properties": {"name": {"description": "Output only. Resource name of this binding. Format: accounts/{account}/accessBindings/{access_binding} or properties/{property}/accessBindings/{access_binding} Example: \"accounts/100/accessBindings/200\"", "readOnly": true, "type": "string"}, "roles": {"description": "A list of roles for to grant to the parent resource. Valid values: predefinedRoles/viewer predefinedRoles/analyst predefinedRoles/editor predefinedRoles/admin predefinedRoles/no-cost-data predefinedRoles/no-revenue-data For users, if an empty list of roles is set, this AccessBinding will be deleted.", "items": {"type": "string"}, "type": "array"}, "user": {"description": "If set, the email address of the user to set roles for. Format: \"<EMAIL>\"", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessDateRange": {"description": "A contiguous range of days: startDate, startDate + 1, ..., endDate.", "id": "GoogleAnalyticsAdminV1alphaAccessDateRange", "properties": {"endDate": {"description": "The inclusive end date for the query in the format `YYYY-MM-DD`. Cannot be before `startDate`. The format `NdaysAgo`, `yesterday`, or `today` is also accepted, and in that case, the date is inferred based on the current time in the request's time zone.", "type": "string"}, "startDate": {"description": "The inclusive start date for the query in the format `YYYY-MM-DD`. Cannot be after `endDate`. The format `NdaysAgo`, `yesterday`, or `today` is also accepted, and in that case, the date is inferred based on the current time in the request's time zone.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessDimension": {"description": "Dimensions are attributes of your data. For example, the dimension `userEmail` indicates the email of the user that accessed reporting data. Dimension values in report responses are strings.", "id": "GoogleAnalyticsAdminV1alphaAccessDimension", "properties": {"dimensionName": {"description": "The API name of the dimension. See [Data Access Schema](https://developers.google.com/analytics/devguides/config/admin/v1/access-api-schema) for the list of dimensions supported in this API. Dimensions are referenced by name in `dimensionFilter` and `orderBys`.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessDimensionHeader": {"description": "Describes a dimension column in the report. Dimensions requested in a report produce column entries within rows and DimensionHeaders. However, dimensions used exclusively within filters or expressions do not produce columns in a report; correspondingly, those dimensions do not produce headers.", "id": "GoogleAnalyticsAdminV1alphaAccessDimensionHeader", "properties": {"dimensionName": {"description": "The dimension's name; for example 'userEmail'.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessDimensionValue": {"description": "The value of a dimension.", "id": "GoogleAnalyticsAdminV1alphaAccessDimensionValue", "properties": {"value": {"description": "The dimension value. For example, this value may be 'France' for the 'country' dimension.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessFilter": {"description": "An expression to filter dimension or metric values.", "id": "GoogleAnalyticsAdminV1alphaAccessFilter", "properties": {"betweenFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBetweenFilter", "description": "A filter for two values."}, "fieldName": {"description": "The dimension name or metric name.", "type": "string"}, "inListFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAccessInListFilter", "description": "A filter for in list values."}, "numericFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAccessNumericFilter", "description": "A filter for numeric or date values."}, "stringFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAccessStringFilter", "description": "Strings related filter."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessFilterExpression": {"description": "Expresses dimension or metric filters. The fields in the same expression need to be either all dimensions or all metrics.", "id": "GoogleAnalyticsAdminV1alphaAccessFilterExpression", "properties": {"accessFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAccessFilter", "description": "A primitive filter. In the same FilterExpression, all of the filter's field names need to be either all dimensions or all metrics."}, "andGroup": {"$ref": "GoogleAnalyticsAdminV1alphaAccessFilterExpressionList", "description": "Each of the FilterExpressions in the and_group has an AND relationship."}, "notExpression": {"$ref": "GoogleAnalyticsAdminV1alphaAccessFilterExpression", "description": "The FilterExpression is NOT of not_expression."}, "orGroup": {"$ref": "GoogleAnalyticsAdminV1alphaAccessFilterExpressionList", "description": "Each of the FilterExpressions in the or_group has an OR relationship."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessFilterExpressionList": {"description": "A list of filter expressions.", "id": "GoogleAnalyticsAdminV1alphaAccessFilterExpressionList", "properties": {"expressions": {"description": "A list of filter expressions.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessFilterExpression"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessInListFilter": {"description": "The result needs to be in a list of string values.", "id": "GoogleAnalyticsAdminV1alphaAccessInListFilter", "properties": {"caseSensitive": {"description": "If true, the string value is case sensitive.", "type": "boolean"}, "values": {"description": "The list of string values. Must be non-empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessMetric": {"description": "The quantitative measurements of a report. For example, the metric `accessCount` is the total number of data access records.", "id": "GoogleAnalyticsAdminV1alphaAccessMetric", "properties": {"metricName": {"description": "The API name of the metric. See [Data Access Schema](https://developers.google.com/analytics/devguides/config/admin/v1/access-api-schema) for the list of metrics supported in this API. Metrics are referenced by name in `metricFilter` & `orderBys`.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessMetricHeader": {"description": "Describes a metric column in the report. Visible metrics requested in a report produce column entries within rows and MetricHeaders. However, metrics used exclusively within filters or expressions do not produce columns in a report; correspondingly, those metrics do not produce headers.", "id": "GoogleAnalyticsAdminV1alphaAccessMetricHeader", "properties": {"metricName": {"description": "The metric's name; for example 'accessCount'.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessMetricValue": {"description": "The value of a metric.", "id": "GoogleAnalyticsAdminV1alphaAccessMetricValue", "properties": {"value": {"description": "The measurement value. For example, this value may be '13'.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessNumericFilter": {"description": "Filters for numeric or date values.", "id": "GoogleAnalyticsAdminV1alphaAccessNumericFilter", "properties": {"operation": {"description": "The operation type for this filter.", "enum": ["OPERATION_UNSPECIFIED", "EQUAL", "LESS_THAN", "LESS_THAN_OR_EQUAL", "GREATER_THAN", "GREATER_THAN_OR_EQUAL"], "enumDescriptions": ["Unspecified.", "Equal", "Less than", "Less than or equal", "Greater than", "Greater than or equal"], "type": "string"}, "value": {"$ref": "GoogleAnalyticsAdminV1alphaNumericValue", "description": "A numeric value or a date value."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessOrderBy": {"description": "Order bys define how rows will be sorted in the response. For example, ordering rows by descending access count is one ordering, and ordering rows by the country string is a different ordering.", "id": "GoogleAnalyticsAdminV1alphaAccessOrderBy", "properties": {"desc": {"description": "If true, sorts by descending order. If false or unspecified, sorts in ascending order.", "type": "boolean"}, "dimension": {"$ref": "GoogleAnalyticsAdminV1alphaAccessOrderByDimensionOrderBy", "description": "Sorts results by a dimension's values."}, "metric": {"$ref": "GoogleAnalyticsAdminV1alphaAccessOrderByMetricOrderBy", "description": "Sorts results by a metric's values."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessOrderByDimensionOrderBy": {"description": "Sorts by dimension values.", "id": "GoogleAnalyticsAdminV1alphaAccessOrderByDimensionOrderBy", "properties": {"dimensionName": {"description": "A dimension name in the request to order by.", "type": "string"}, "orderType": {"description": "Controls the rule for dimension value ordering.", "enum": ["ORDER_TYPE_UNSPECIFIED", "ALPHANUMERIC", "CASE_INSENSITIVE_ALPHANUMERIC", "NUMERIC"], "enumDescriptions": ["Unspecified.", "Alphanumeric sort by Unicode code point. For example, \"2\" < \"A\" < \"X\" < \"b\" < \"z\".", "Case insensitive alphanumeric sort by lower case Unicode code point. For example, \"2\" < \"A\" < \"b\" < \"X\" < \"z\".", "Dimension values are converted to numbers before sorting. For example in NUMERIC sort, \"25\" < \"100\", and in `ALPHANUMERIC` sort, \"100\" < \"25\". Non-numeric dimension values all have equal ordering value below all numeric values."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessOrderByMetricOrderBy": {"description": "Sorts by metric values.", "id": "GoogleAnalyticsAdminV1alphaAccessOrderByMetricOrderBy", "properties": {"metricName": {"description": "A metric name in the request to order by.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessQuota": {"description": "Current state of all quotas for this Analytics property. If any quota for a property is exhausted, all requests to that property will return Resource Exhausted errors.", "id": "GoogleAnalyticsAdminV1alphaAccessQuota", "properties": {"concurrentRequests": {"$ref": "GoogleAnalyticsAdminV1alphaAccessQuotaStatus", "description": "Properties can use up to 50 concurrent requests."}, "serverErrorsPerProjectPerHour": {"$ref": "GoogleAnalyticsAdminV1alphaAccessQuotaStatus", "description": "Properties and cloud project pairs can have up to 50 server errors per hour."}, "tokensPerDay": {"$ref": "GoogleAnalyticsAdminV1alphaAccessQuotaStatus", "description": "Properties can use 250,000 tokens per day. Most requests consume fewer than 10 tokens."}, "tokensPerHour": {"$ref": "GoogleAnalyticsAdminV1alphaAccessQuotaStatus", "description": "Properties can use 50,000 tokens per hour. An API request consumes a single number of tokens, and that number is deducted from all of the hourly, daily, and per project hourly quotas."}, "tokensPerProjectPerHour": {"$ref": "GoogleAnalyticsAdminV1alphaAccessQuotaStatus", "description": "Properties can use up to 25% of their tokens per project per hour. This amounts to Analytics 360 Properties can use 12,500 tokens per project per hour. An API request consumes a single number of tokens, and that number is deducted from all of the hourly, daily, and per project hourly quotas."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessQuotaStatus": {"description": "Current state for a particular quota group.", "id": "GoogleAnalyticsAdminV1alphaAccessQuotaStatus", "properties": {"consumed": {"description": "<PERSON><PERSON><PERSON> consumed by this request.", "format": "int32", "type": "integer"}, "remaining": {"description": "<PERSON><PERSON><PERSON> remaining after this request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessRow": {"description": "Access report data for each row.", "id": "GoogleAnalyticsAdminV1alphaAccessRow", "properties": {"dimensionValues": {"description": "List of dimension values. These values are in the same order as specified in the request.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessDimensionValue"}, "type": "array"}, "metricValues": {"description": "List of metric values. These values are in the same order as specified in the request.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessMetricValue"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccessStringFilter": {"description": "The filter for strings.", "id": "GoogleAnalyticsAdminV1alphaAccessStringFilter", "properties": {"caseSensitive": {"description": "If true, the string value is case sensitive.", "type": "boolean"}, "matchType": {"description": "The match type for this filter.", "enum": ["MATCH_TYPE_UNSPECIFIED", "EXACT", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "FULL_REGEXP", "PARTIAL_REGEXP"], "enumDescriptions": ["Unspecified", "Exact match of the string value.", "Begins with the string value.", "Ends with the string value.", "Contains the string value.", "Full match for the regular expression with the string value.", "Partial match for the regular expression with the string value."], "type": "string"}, "value": {"description": "The string value used for the matching.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccount": {"description": "A resource message representing a Google Analytics account.", "id": "GoogleAnalyticsAdminV1alphaAccount", "properties": {"createTime": {"description": "Output only. Time when this account was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleted": {"description": "Output only. Indicates whether this Account is soft-deleted or not. Deleted accounts are excluded from List results unless specifically requested.", "readOnly": true, "type": "boolean"}, "displayName": {"description": "Required. Human-readable display name for this account.", "type": "string"}, "gmpOrganization": {"description": "Output only. The URI for a Google Marketing Platform organization resource. Only set when this account is connected to a GMP organization. Format: marketingplatformadmin.googleapis.com/organizations/{org_id}", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Resource name of this account. Format: accounts/{account} Example: \"accounts/100\"", "readOnly": true, "type": "string"}, "regionCode": {"description": "Country of business. Must be a Unicode CLDR region code.", "type": "string"}, "updateTime": {"description": "Output only. Time when account payload fields were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAccountSummary": {"description": "A virtual resource representing an overview of an account and all its child Google Analytics properties.", "id": "GoogleAnalyticsAdminV1alphaAccountSummary", "properties": {"account": {"description": "Resource name of account referred to by this account summary Format: accounts/{account_id} Example: \"accounts/1000\"", "type": "string"}, "displayName": {"description": "Display name for the account referred to in this account summary.", "type": "string"}, "name": {"description": "Resource name for this account summary. Format: accountSummaries/{account_id} Example: \"accountSummaries/1000\"", "type": "string"}, "propertySummaries": {"description": "List of summaries for child accounts of this account.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaPropertySummary"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAcknowledgeUserDataCollectionRequest": {"description": "Request message for AcknowledgeUserDataCollection RPC.", "id": "GoogleAnalyticsAdminV1alphaAcknowledgeUserDataCollectionRequest", "properties": {"acknowledgement": {"description": "Required. An acknowledgement that the caller of this method understands the terms of user data collection. This field must contain the exact value: \"I acknowledge that I have the necessary privacy disclosures and rights from my end users for the collection and processing of their data, including the association of such data with the visitation information Google Analytics collects from my site and/or app property.\"", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAcknowledgeUserDataCollectionResponse": {"description": "Response message for AcknowledgeUserDataCollection RPC.", "id": "GoogleAnalyticsAdminV1alphaAcknowledgeUserDataCollectionResponse", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAdSenseLink": {"description": "A link between a Google Analytics property and an AdSense for Content ad client.", "id": "GoogleAnalyticsAdminV1alphaAdSenseLink", "properties": {"adClientCode": {"description": "Immutable. The AdSense ad client code that the Google Analytics property is linked to. Example format: \"ca-pub-1234567890\"", "type": "string"}, "name": {"description": "Output only. The resource name for this AdSense Link resource. Format: properties/{propertyId}/adSenseLinks/{linkId} Example: properties/1234/adSenseLinks/6789", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaApproveDisplayVideo360AdvertiserLinkProposalRequest": {"description": "Request message for ApproveDisplayVideo360AdvertiserLinkProposal RPC.", "id": "GoogleAnalyticsAdminV1alphaApproveDisplayVideo360AdvertiserLinkProposalRequest", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1alphaApproveDisplayVideo360AdvertiserLinkProposalResponse": {"description": "Response message for ApproveDisplayVideo360AdvertiserLinkProposal RPC.", "id": "GoogleAnalyticsAdminV1alphaApproveDisplayVideo360AdvertiserLinkProposalResponse", "properties": {"displayVideo360AdvertiserLink": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink", "description": "The DisplayVideo360AdvertiserLink created as a result of approving the proposal."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaArchiveAudienceRequest": {"description": "Request message for ArchiveAudience RPC.", "id": "GoogleAnalyticsAdminV1alphaArchiveAudienceRequest", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1alphaArchiveCustomDimensionRequest": {"description": "Request message for ArchiveCustomDimension RPC.", "id": "GoogleAnalyticsAdminV1alphaArchiveCustomDimensionRequest", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1alphaArchiveCustomMetricRequest": {"description": "Request message for ArchiveCustomMetric RPC.", "id": "GoogleAnalyticsAdminV1alphaArchiveCustomMetricRequest", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAttributionSettings": {"description": "The attribution settings used for a given property. This is a singleton resource.", "id": "GoogleAnalyticsAdminV1alphaAttributionSettings", "properties": {"acquisitionConversionEventLookbackWindow": {"description": "Required. The lookback window configuration for acquisition conversion events. The default window size is 30 days.", "enum": ["ACQUISITION_CONVERSION_EVENT_LOOKBACK_WINDOW_UNSPECIFIED", "ACQUISITION_CONVERSION_EVENT_LOOKBACK_WINDOW_7_DAYS", "ACQUISITION_CONVERSION_EVENT_LOOKBACK_WINDOW_30_DAYS"], "enumDescriptions": ["Lookback window size unspecified.", "7-day lookback window.", "30-day lookback window."], "type": "string"}, "adsWebConversionDataExportScope": {"description": "Required. The Conversion Export Scope for data exported to linked Ads Accounts.", "enum": ["ADS_WEB_CONVERSION_DATA_EXPORT_SCOPE_UNSPECIFIED", "NOT_SELECTED_YET", "PAID_AND_ORGANIC_CHANNELS", "GOOGLE_PAID_CHANNELS"], "enumDescriptions": ["Default value. This value is unused.", "No data export scope selected yet. Export scope can never be changed back to this value.", "Paid and organic channels are eligible to receive conversion credit, but only credit assigned to Google Ads channels will appear in your Ads accounts. To learn more, see [Paid and Organic channels](https://support.google.com/analytics/answer/********).", "Only Google Ads paid channels are eligible to receive conversion credit. To learn more, see [Google Paid channels](https://support.google.com/analytics/answer/********)."], "type": "string"}, "name": {"description": "Output only. Resource name of this attribution settings resource. Format: properties/{property_id}/attributionSettings Example: \"properties/1000/attributionSettings\"", "readOnly": true, "type": "string"}, "otherConversionEventLookbackWindow": {"description": "Required. The lookback window for all other, non-acquisition conversion events. The default window size is 90 days.", "enum": ["OTHER_CONVERSION_EVENT_LOOKBACK_WINDOW_UNSPECIFIED", "OTHER_CONVERSION_EVENT_LOOKBACK_WINDOW_30_DAYS", "OTHER_CONVERSION_EVENT_LOOKBACK_WINDOW_60_DAYS", "OTHER_CONVERSION_EVENT_LOOKBACK_WINDOW_90_DAYS"], "enumDescriptions": ["Lookback window size unspecified.", "30-day lookback window.", "60-day lookback window.", "90-day lookback window."], "type": "string"}, "reportingAttributionModel": {"description": "Required. The reporting attribution model used to calculate conversion credit in this property's reports. Changing the attribution model will apply to both historical and future data. These changes will be reflected in reports with conversion and revenue data. User and session data will be unaffected.", "enum": ["REPORTING_ATTRIBUTION_MODEL_UNSPECIFIED", "PAID_AND_ORGANIC_CHANNELS_DATA_DRIVEN", "PAID_AND_ORGANIC_CHANNELS_LAST_CLICK", "GOOGLE_PAID_CHANNELS_LAST_CLICK"], "enumDescriptions": ["Reporting attribution model unspecified.", "Data-driven attribution distributes credit for the conversion based on data for each conversion event. Each Data-driven model is specific to each advertiser and each conversion event. Previously CROSS_CHANNEL_DATA_DRIVEN", "Ignores direct traffic and attributes 100% of the conversion value to the last channel that the customer clicked through (or engaged view through for YouTube) before converting. Previously CROSS_CHANNEL_LAST_CLICK", "Attributes 100% of the conversion value to the last Google Paid channel that the customer clicked through before converting. Previously ADS_PREFERRED_LAST_CLICK"], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudience": {"description": "A resource message representing an Audience.", "id": "GoogleAnalyticsAdminV1alphaAudience", "properties": {"adsPersonalizationEnabled": {"description": "Output only. It is automatically set by GA to false if this is an NPA Audience and is excluded from ads personalization.", "readOnly": true, "type": "boolean"}, "createTime": {"description": "Output only. Time when the Audience was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Required. The description of the Audience.", "type": "string"}, "displayName": {"description": "Required. The display name of the Audience.", "type": "string"}, "eventTrigger": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceEventTrigger", "description": "Optional. Specifies an event to log when a user joins the Audience. If not set, no event is logged when a user joins the Audience."}, "exclusionDurationMode": {"description": "Immutable. Specifies how long an exclusion lasts for users that meet the exclusion filter. It is applied to all EXCLUDE filter clauses and is ignored when there is no EXCLUDE filter clause in the Audience.", "enum": ["AUDIENCE_EXCLUSION_DURATION_MODE_UNSPECIFIED", "EXCLUDE_TEMPORARILY", "EXCLUDE_PERMANENTLY"], "enumDescriptions": ["Not specified.", "Exclude users from the Audience during periods when they meet the filter clause.", "Exclude users from the Audience if they've ever met the filter clause."], "type": "string"}, "filterClauses": {"description": "Required. Immutable. Unordered list. Filter clauses that define the Audience. All clauses will be AND’ed together.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceFilterClause"}, "type": "array"}, "membershipDurationDays": {"description": "Required. Immutable. The duration a user should stay in an Audience. It cannot be set to more than 540 days.", "format": "int32", "type": "integer"}, "name": {"description": "Output only. The resource name for this Audience resource. Format: properties/{propertyId}/audiences/{audienceId}", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilter": {"description": "A specific filter for a single dimension or metric.", "id": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilter", "properties": {"atAnyPointInTime": {"description": "Optional. Indicates whether this filter needs dynamic evaluation or not. If set to true, users join the Audience if they ever met the condition (static evaluation). If unset or set to false, user evaluation for an Audience is dynamic; users are added to an Audience when they meet the conditions and then removed when they no longer meet them. This can only be set when Audience scope is ACROSS_ALL_SESSIONS.", "type": "boolean"}, "betweenFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterBetweenFilter", "description": "A filter for numeric or date values between certain values on a dimension or metric."}, "fieldName": {"description": "Required. Immutable. The dimension name or metric name to filter. If the field name refers to a custom dimension or metric, a scope prefix will be added to the front of the custom dimensions or metric name. For more on scope prefixes or custom dimensions/metrics, reference the [Google Analytics Data API documentation] (https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema#custom_dimensions).", "type": "string"}, "inAnyNDayPeriod": {"description": "Optional. If set, specifies the time window for which to evaluate data in number of days. If not set, then audience data is evaluated against lifetime data (For example, infinite time window). For example, if set to 1 day, only the current day's data is evaluated. The reference point is the current day when at_any_point_in_time is unset or false. It can only be set when Audience scope is ACROSS_ALL_SESSIONS and cannot be greater than 60 days.", "format": "int32", "type": "integer"}, "inListFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterInListFilter", "description": "A filter for a string dimension that matches a particular list of options."}, "numericFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterNumericFilter", "description": "A filter for numeric or date values on a dimension or metric."}, "stringFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterStringFilter", "description": "A filter for a string-type dimension that matches a particular pattern."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterBetweenFilter": {"description": "A filter for numeric or date values between certain values on a dimension or metric.", "id": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterBetweenFilter", "properties": {"fromValue": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterNumericValue", "description": "Required. <PERSON><PERSON> with this number, inclusive."}, "toValue": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterNumericValue", "description": "Required. Ends with this number, inclusive."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterInListFilter": {"description": "A filter for a string dimension that matches a particular list of options.", "id": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterInListFilter", "properties": {"caseSensitive": {"description": "Optional. If true, the match is case-sensitive. If false, the match is case-insensitive.", "type": "boolean"}, "values": {"description": "Required. The list of possible string values to match against. Must be non-empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterNumericFilter": {"description": "A filter for numeric or date values on a dimension or metric.", "id": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterNumericFilter", "properties": {"operation": {"description": "Required. The operation applied to a numeric filter.", "enum": ["OPERATION_UNSPECIFIED", "EQUAL", "LESS_THAN", "GREATER_THAN"], "enumDescriptions": ["Unspecified.", "Equal.", "Less than.", "Greater than."], "type": "string"}, "value": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterNumericValue", "description": "Required. The numeric or date value to match against."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterNumericValue": {"description": "To represent a number.", "id": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterNumericValue", "properties": {"doubleValue": {"description": "Double value.", "format": "double", "type": "number"}, "int64Value": {"description": "Integer value.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterStringFilter": {"description": "A filter for a string-type dimension that matches a particular pattern.", "id": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilterStringFilter", "properties": {"caseSensitive": {"description": "Optional. If true, the match is case-sensitive. If false, the match is case-insensitive.", "type": "boolean"}, "matchType": {"description": "Required. The match type for the string filter.", "enum": ["MATCH_TYPE_UNSPECIFIED", "EXACT", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "FULL_REGEXP"], "enumDescriptions": ["Unspecified", "Exact match of the string value.", "Begins with the string value.", "Ends with the string value.", "Contains the string value.", "Full regular expression matches with the string value."], "type": "string"}, "value": {"description": "Required. The string value to be matched against.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceEventFilter": {"description": "A filter that matches events of a single event name. If an event parameter is specified, only the subset of events that match both the single event name and the parameter filter expressions match this event filter.", "id": "GoogleAnalyticsAdminV1alphaAudienceEventFilter", "properties": {"eventName": {"description": "Required. Immutable. The name of the event to match against.", "type": "string"}, "eventParameterFilterExpression": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceFilterExpression", "description": "Optional. If specified, this filter matches events that match both the single event name and the parameter filter expressions. AudienceEventFilter inside the parameter filter expression cannot be set (For example, nested event filters are not supported). This should be a single and_group of dimension_or_metric_filter or not_expression; ANDs of ORs are not supported. Also, if it includes a filter for \"eventCount\", only that one will be considered; all the other filters will be ignored."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceEventTrigger": {"description": "Specifies an event to log when a user joins the Audience.", "id": "GoogleAnalyticsAdminV1alphaAudienceEventTrigger", "properties": {"eventName": {"description": "Required. The event name that will be logged.", "type": "string"}, "logCondition": {"description": "Required. When to log the event.", "enum": ["LOG_CONDITION_UNSPECIFIED", "AUDIENCE_JOINED", "AUDIENCE_MEMBERSHIP_RENEWED"], "enumDescriptions": ["Log condition is not specified.", "The event should be logged only when a user is joined.", "The event should be logged whenever the Audience condition is met, even if the user is already a member of the Audience."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceFilterClause": {"description": "A clause for defining either a simple or sequence filter. A filter can be inclusive (For example, users satisfying the filter clause are included in the Audience) or exclusive (For example, users satisfying the filter clause are excluded from the Audience).", "id": "GoogleAnalyticsAdminV1alphaAudienceFilterClause", "properties": {"clauseType": {"description": "Required. Specifies whether this is an include or exclude filter clause.", "enum": ["AUDIENCE_CLAUSE_TYPE_UNSPECIFIED", "INCLUDE", "EXCLUDE"], "enumDescriptions": ["Unspecified clause type.", "Users will be included in the Audience if the filter clause is met.", "Users will be excluded from the Audience if the filter clause is met."], "type": "string"}, "sequenceFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceSequenceFilter", "description": "Filters that must occur in a specific order for the user to be a member of the Audience."}, "simpleFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceSimpleFilter", "description": "A simple filter that a user must satisfy to be a member of the Audience."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceFilterExpression": {"description": "A logical expression of Audience dimension, metric, or event filters.", "id": "GoogleAnalyticsAdminV1alphaAudienceFilterExpression", "properties": {"andGroup": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceFilterExpressionList", "description": "A list of expressions to be AND’ed together. It can only contain AudienceFilterExpressions with or_group. This must be set for the top level AudienceFilterExpression."}, "dimensionOrMetricFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceDimensionOrMetricFilter", "description": "A filter on a single dimension or metric. This cannot be set on the top level AudienceFilterExpression."}, "eventFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceEventFilter", "description": "Creates a filter that matches a specific event. This cannot be set on the top level AudienceFilterExpression."}, "notExpression": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceFilterExpression", "description": "A filter expression to be NOT'ed (For example, inverted, complemented). It can only include a dimension_or_metric_filter. This cannot be set on the top level AudienceFilterExpression."}, "orGroup": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceFilterExpressionList", "description": "A list of expressions to OR’ed together. It cannot contain AudienceFilterExpressions with and_group or or_group."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceFilterExpressionList": {"description": "A list of Audience filter expressions.", "id": "GoogleAnalyticsAdminV1alphaAudienceFilterExpressionList", "properties": {"filterExpressions": {"description": "A list of Audience filter expressions.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceFilterExpression"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceSequenceFilter": {"description": "Defines filters that must occur in a specific order for the user to be a member of the Audience.", "id": "GoogleAnalyticsAdminV1alphaAudienceSequenceFilter", "properties": {"scope": {"description": "Required. Immutable. Specifies the scope for this filter.", "enum": ["AUDIENCE_FILTER_SCOPE_UNSPECIFIED", "AUDIENCE_FILTER_SCOPE_WITHIN_SAME_EVENT", "AUDIENCE_FILTER_SCOPE_WITHIN_SAME_SESSION", "AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS"], "enumDescriptions": ["Scope is not specified.", "User joins the Audience if the filter condition is met within one event.", "User joins the Audience if the filter condition is met within one session.", "User joins the Audience if the filter condition is met by any event across any session."], "type": "string"}, "sequenceMaximumDuration": {"description": "Optional. Defines the time period in which the whole sequence must occur.", "format": "google-duration", "type": "string"}, "sequenceSteps": {"description": "Required. An ordered sequence of steps. A user must complete each step in order to join the sequence filter.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceSequenceFilterAudienceSequenceStep"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceSequenceFilterAudienceSequenceStep": {"description": "A condition that must occur in the specified step order for this user to match the sequence.", "id": "GoogleAnalyticsAdminV1alphaAudienceSequenceFilterAudienceSequenceStep", "properties": {"constraintDuration": {"description": "Optional. When set, this step must be satisfied within the constraint_duration of the previous step (For example, t[i] - t[i-1] <= constraint_duration). If not set, there is no duration requirement (the duration is effectively unlimited). It is ignored for the first step.", "format": "google-duration", "type": "string"}, "filterExpression": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceFilterExpression", "description": "Required. Immutable. A logical expression of Audience dimension, metric, or event filters in each step."}, "immediatelyFollows": {"description": "Optional. If true, the event satisfying this step must be the very next event after the event satisfying the last step. If unset or false, this step indirectly follows the prior step; for example, there may be events between the prior step and this step. It is ignored for the first step.", "type": "boolean"}, "scope": {"description": "Required. Immutable. Specifies the scope for this step.", "enum": ["AUDIENCE_FILTER_SCOPE_UNSPECIFIED", "AUDIENCE_FILTER_SCOPE_WITHIN_SAME_EVENT", "AUDIENCE_FILTER_SCOPE_WITHIN_SAME_SESSION", "AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS"], "enumDescriptions": ["Scope is not specified.", "User joins the Audience if the filter condition is met within one event.", "User joins the Audience if the filter condition is met within one session.", "User joins the Audience if the filter condition is met by any event across any session."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaAudienceSimpleFilter": {"description": "Defines a simple filter that a user must satisfy to be a member of the Audience.", "id": "GoogleAnalyticsAdminV1alphaAudienceSimpleFilter", "properties": {"filterExpression": {"$ref": "GoogleAnalyticsAdminV1alphaAudienceFilterExpression", "description": "Required. Immutable. A logical expression of Audience dimension, metric, or event filters."}, "scope": {"description": "Required. Immutable. Specifies the scope for this filter.", "enum": ["AUDIENCE_FILTER_SCOPE_UNSPECIFIED", "AUDIENCE_FILTER_SCOPE_WITHIN_SAME_EVENT", "AUDIENCE_FILTER_SCOPE_WITHIN_SAME_SESSION", "AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS"], "enumDescriptions": ["Scope is not specified.", "User joins the Audience if the filter condition is met within one event.", "User joins the Audience if the filter condition is met within one session.", "User joins the Audience if the filter condition is met by any event across any session."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaBatchCreateAccessBindingsRequest": {"description": "Request message for BatchCreateAccessBindings RPC.", "id": "GoogleAnalyticsAdminV1alphaBatchCreateAccessBindingsRequest", "properties": {"requests": {"description": "Required. The requests specifying the access bindings to create. A maximum of 1000 access bindings can be created in a batch.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaCreateAccessBindingRequest"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaBatchCreateAccessBindingsResponse": {"description": "Response message for BatchCreateAccessBindings RPC.", "id": "GoogleAnalyticsAdminV1alphaBatchCreateAccessBindingsResponse", "properties": {"accessBindings": {"description": "The access bindings created.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaBatchDeleteAccessBindingsRequest": {"description": "Request message for BatchDeleteAccessBindings RPC.", "id": "GoogleAnalyticsAdminV1alphaBatchDeleteAccessBindingsRequest", "properties": {"requests": {"description": "Required. The requests specifying the access bindings to delete. A maximum of 1000 access bindings can be deleted in a batch.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaDeleteAccessBindingRequest"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaBatchGetAccessBindingsResponse": {"description": "Response message for BatchGetAccessBindings RPC.", "id": "GoogleAnalyticsAdminV1alphaBatchGetAccessBindingsResponse", "properties": {"accessBindings": {"description": "The requested access bindings.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaBatchUpdateAccessBindingsRequest": {"description": "Request message for BatchUpdateAccessBindings RPC.", "id": "GoogleAnalyticsAdminV1alphaBatchUpdateAccessBindingsRequest", "properties": {"requests": {"description": "Required. The requests specifying the access bindings to update. A maximum of 1000 access bindings can be updated in a batch.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaUpdateAccessBindingRequest"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaBatchUpdateAccessBindingsResponse": {"description": "Response message for BatchUpdateAccessBindings RPC.", "id": "GoogleAnalyticsAdminV1alphaBatchUpdateAccessBindingsResponse", "properties": {"accessBindings": {"description": "The access bindings updated.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaBigQueryLink": {"description": "A link between a Google Analytics property and BigQuery project.", "id": "GoogleAnalyticsAdminV1alphaBigQueryLink", "properties": {"createTime": {"description": "Output only. Time when the link was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dailyExportEnabled": {"description": "If set true, enables daily data export to the linked Google Cloud project.", "type": "boolean"}, "datasetLocation": {"description": "Required. Immutable. The geographic location where the created BigQuery dataset should reside. See https://cloud.google.com/bigquery/docs/locations for supported locations.", "type": "string"}, "excludedEvents": {"description": "The list of event names that will be excluded from exports.", "items": {"type": "string"}, "type": "array"}, "exportStreams": {"description": "The list of streams under the parent property for which data will be exported. Format: properties/{property_id}/dataStreams/{stream_id} Example: ['properties/1000/dataStreams/2000']", "items": {"type": "string"}, "type": "array"}, "freshDailyExportEnabled": {"description": "If set true, enables fresh daily export to the linked Google Cloud project.", "type": "boolean"}, "includeAdvertisingId": {"description": "If set true, exported data will include advertising identifiers for mobile app streams.", "type": "boolean"}, "name": {"description": "Output only. Resource name of this BigQuery link. Format: 'properties/{property_id}/bigQueryLinks/{bigquery_link_id}' Format: 'properties/1234/bigQueryLinks/abc567'", "readOnly": true, "type": "string"}, "project": {"description": "Immutable. The linked Google Cloud project. When creating a BigQueryLink, you may provide this resource name using either a project number or project ID. Once this resource has been created, the returned project will always have a project that contains a project number. Format: 'projects/{project number}' Example: 'projects/1234'", "type": "string"}, "streamingExportEnabled": {"description": "If set true, enables streaming export to the linked Google Cloud project.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCalculatedMetric": {"description": "A definition for a calculated metric.", "id": "GoogleAnalyticsAdminV1alphaCalculatedMetric", "properties": {"calculatedMetricId": {"description": "Output only. The ID to use for the calculated metric. In the UI, this is referred to as the \"API name.\" The calculated_metric_id is used when referencing this calculated metric from external APIs. For example, \"calcMetric:{calculated_metric_id}\".", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description for this calculated metric. Max length of 4096 characters.", "type": "string"}, "displayName": {"description": "Required. Display name for this calculated metric as shown in the Google Analytics UI. Max length 82 characters.", "type": "string"}, "formula": {"description": "Required. The calculated metric's definition. Maximum number of unique referenced custom metrics is 5. Formulas supports the following operations: + (addition), - (subtraction), - (negative), * (multiplication), / (division), () (parenthesis). Any valid real numbers are acceptable that fit in a Long (64bit integer) or a Double (64 bit floating point number). Example formula: \"( customEvent:parameter_name + cartPurchaseQuantity ) / 2.0\"", "type": "string"}, "invalidMetricReference": {"description": "Output only. If true, this calculated metric has a invalid metric reference. Anything using a calculated metric with invalid_metric_reference set to true may fail, produce warnings, or produce unexpected results.", "readOnly": true, "type": "boolean"}, "metricUnit": {"description": "Required. The type for the calculated metric's value.", "enum": ["METRIC_UNIT_UNSPECIFIED", "STANDARD", "CURRENCY", "FEET", "MILES", "METERS", "KILOMETERS", "MILLISECONDS", "SECONDS", "MINUTES", "HOURS"], "enumDescriptions": ["MetricU<PERSON><PERSON> unspecified or missing.", "This metric uses default units.", "This metric measures a currency.", "This metric measures feet.", "This metric measures miles.", "This metric measures meters.", "This metric measures kilometers.", "This metric measures milliseconds.", "This metric measures seconds.", "This metric measures minutes.", "This metric measures hours."], "type": "string"}, "name": {"description": "Output only. Resource name for this CalculatedMetric. Format: 'properties/{property_id}/calculatedMetrics/{calculated_metric_id}'", "readOnly": true, "type": "string"}, "restrictedMetricType": {"description": "Output only. Types of restricted data that this metric contains.", "items": {"enum": ["RESTRICTED_METRIC_TYPE_UNSPECIFIED", "COST_DATA", "REVENUE_DATA"], "enumDescriptions": ["Type unknown or unspecified.", "Metric reports cost data.", "Metric reports revenue data."], "type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCancelDisplayVideo360AdvertiserLinkProposalRequest": {"description": "Request message for CancelDisplayVideo360AdvertiserLinkProposal RPC.", "id": "GoogleAnalyticsAdminV1alphaCancelDisplayVideo360AdvertiserLinkProposalRequest", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChangeHistoryChange": {"description": "A description of a change to a single Google Analytics resource.", "id": "GoogleAnalyticsAdminV1alphaChangeHistoryChange", "properties": {"action": {"description": "The type of action that changed this resource.", "enum": ["ACTION_TYPE_UNSPECIFIED", "CREATED", "UPDATED", "DELETED"], "enumDescriptions": ["Action type unknown or not specified.", "Resource was created in this change.", "Resource was updated in this change.", "Resource was deleted in this change."], "type": "string"}, "resource": {"description": "Resource name of the resource whose changes are described by this entry.", "type": "string"}, "resourceAfterChange": {"$ref": "GoogleAnalyticsAdminV1alphaChangeHistoryChangeChangeHistoryResource", "description": "Resource contents from after the change was made. If this resource was deleted in this change, this field will be missing."}, "resourceBeforeChange": {"$ref": "GoogleAnalyticsAdminV1alphaChangeHistoryChangeChangeHistoryResource", "description": "Resource contents from before the change was made. If this resource was created in this change, this field will be missing."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChangeHistoryChangeChangeHistoryResource": {"description": "A snapshot of a resource as before or after the result of a change in change history.", "id": "GoogleAnalyticsAdminV1alphaChangeHistoryChangeChangeHistoryResource", "properties": {"account": {"$ref": "GoogleAnalyticsAdminV1alphaAccount", "description": "A snapshot of an Account resource in change history."}, "adsenseLink": {"$ref": "GoogleAnalyticsAdminV1alphaAdSenseLink", "description": "A snapshot of an AdSenseLink resource in change history."}, "attributionSettings": {"$ref": "GoogleAnalyticsAdminV1alphaAttributionSettings", "description": "A snapshot of AttributionSettings resource in change history."}, "audience": {"$ref": "GoogleAnalyticsAdminV1alphaAudience", "description": "A snapshot of an Audience resource in change history."}, "bigqueryLink": {"$ref": "GoogleAnalyticsAdminV1alphaBigQueryLink", "description": "A snapshot of a BigQuery link resource in change history."}, "calculatedMetric": {"$ref": "GoogleAnalyticsAdminV1alphaCalculatedMetric", "description": "A snapshot of a CalculatedMetric resource in change history."}, "channelGroup": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroup", "description": "A snapshot of a ChannelGroup resource in change history."}, "conversionEvent": {"$ref": "GoogleAnalyticsAdminV1alphaConversionEvent", "description": "A snapshot of a ConversionEvent resource in change history."}, "customDimension": {"$ref": "GoogleAnalyticsAdminV1alphaCustomDimension", "description": "A snapshot of a CustomDimension resource in change history."}, "customMetric": {"$ref": "GoogleAnalyticsAdminV1alphaCustomMetric", "description": "A snapshot of a CustomMetric resource in change history."}, "dataRedactionSettings": {"$ref": "GoogleAnalyticsAdminV1alphaDataRedactionSettings", "description": "A snapshot of DataRedactionSettings resource in change history."}, "dataRetentionSettings": {"$ref": "GoogleAnalyticsAdminV1alphaDataRetentionSettings", "description": "A snapshot of a data retention settings resource in change history."}, "dataStream": {"$ref": "GoogleAnalyticsAdminV1alphaDataStream", "description": "A snapshot of a DataStream resource in change history."}, "displayVideo360AdvertiserLink": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink", "description": "A snapshot of a DisplayVideo360AdvertiserLink resource in change history."}, "displayVideo360AdvertiserLinkProposal": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLinkProposal", "description": "A snapshot of a DisplayVideo360AdvertiserLinkProposal resource in change history."}, "enhancedMeasurementSettings": {"$ref": "GoogleAnalyticsAdminV1alphaEnhancedMeasurementSettings", "description": "A snapshot of EnhancedMeasurementSettings resource in change history."}, "eventCreateRule": {"$ref": "GoogleAnalyticsAdminV1alphaEventCreateRule", "description": "A snapshot of an EventCreateRule resource in change history."}, "expandedDataSet": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSet", "description": "A snapshot of an ExpandedDataSet resource in change history."}, "firebaseLink": {"$ref": "GoogleAnalyticsAdminV1alphaFirebaseLink", "description": "A snapshot of a FirebaseLink resource in change history."}, "googleAdsLink": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleAdsLink", "description": "A snapshot of a GoogleAdsLink resource in change history."}, "googleSignalsSettings": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleSignalsSettings", "description": "A snapshot of a GoogleSignalsSettings resource in change history."}, "keyEvent": {"$ref": "GoogleAnalyticsAdminV1alphaKeyEvent", "description": "A snapshot of a KeyEvent resource in change history."}, "measurementProtocolSecret": {"$ref": "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret", "description": "A snapshot of a MeasurementProtocolSecret resource in change history."}, "property": {"$ref": "GoogleAnalyticsAdminV1alphaProperty", "description": "A snapshot of a Property resource in change history."}, "reportingDataAnnotation": {"$ref": "GoogleAnalyticsAdminV1alphaReportingDataAnnotation", "description": "A snapshot of a ReportingDataAnnotation resource in change history."}, "searchAds360Link": {"$ref": "GoogleAnalyticsAdminV1alphaSearchAds360Link", "description": "A snapshot of a SearchAds360Link resource in change history."}, "skadnetworkConversionValueSchema": {"$ref": "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema", "description": "A snapshot of SKAdNetworkConversionValueSchema resource in change history."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChangeHistoryEvent": {"description": "A set of changes within a Google Analytics account or its child properties that resulted from the same cause. Common causes would be updates made in the Google Analytics UI, changes from customer support, or automatic Google Analytics system changes.", "id": "GoogleAnalyticsAdminV1alphaChangeHistoryEvent", "properties": {"actorType": {"description": "The type of actor that made this change.", "enum": ["ACTOR_TYPE_UNSPECIFIED", "USER", "SYSTEM", "SUPPORT"], "enumDescriptions": ["Unknown or unspecified actor type.", "Changes made by the user specified in actor_email.", "Changes made by the Google Analytics system.", "Changes made by Google Analytics support team staff."], "type": "string"}, "changeTime": {"description": "Time when change was made.", "format": "google-datetime", "type": "string"}, "changes": {"description": "A list of changes made in this change history event that fit the filters specified in SearchChangeHistoryEventsRequest.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaChangeHistoryChange"}, "type": "array"}, "changesFiltered": {"description": "If true, then the list of changes returned was filtered, and does not represent all changes that occurred in this event.", "type": "boolean"}, "id": {"description": "ID of this change history event. This ID is unique across Google Analytics.", "type": "string"}, "userActorEmail": {"description": "Email address of the Google account that made the change. This will be a valid email address if the actor field is set to USER, and empty otherwise. Google accounts that have been deleted will cause an error.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChannelGroup": {"description": "A resource message representing a Channel Group.", "id": "GoogleAnalyticsAdminV1alphaChannelGroup", "properties": {"description": {"description": "The description of the Channel Group. Max length of 256 characters.", "type": "string"}, "displayName": {"description": "Required. The display name of the Channel Group. Max length of 80 characters.", "type": "string"}, "groupingRule": {"description": "Required. The grouping rules of channels. Maximum number of rules is 50.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaGroupingRule"}, "type": "array"}, "name": {"description": "Output only. The resource name for this Channel Group resource. Format: properties/{property}/channelGroups/{channel_group}", "readOnly": true, "type": "string"}, "primary": {"description": "Optional. If true, this channel group will be used as the default channel group for reports. Only one channel group can be set as `primary` at any time. If the `primary` field gets set on a channel group, it will get unset on the previous primary channel group. The Google Analytics predefined channel group is the primary by default.", "type": "boolean"}, "systemDefined": {"description": "Output only. If true, then this channel group is the Default Channel Group predefined by Google Analytics. Display name and grouping rules cannot be updated for this channel group.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChannelGroupFilter": {"description": "A specific filter for a single dimension.", "id": "GoogleAnalyticsAdminV1alphaChannelGroupFilter", "properties": {"fieldName": {"description": "Required. Immutable. The dimension name to filter.", "type": "string"}, "inListFilter": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroupFilterInListFilter", "description": "A filter for a string dimension that matches a particular list of options."}, "stringFilter": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroupFilterStringFilter", "description": "A filter for a string-type dimension that matches a particular pattern."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpression": {"description": "A logical expression of Channel Group dimension filters.", "id": "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpression", "properties": {"andGroup": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpressionList", "description": "A list of expressions to be AND’ed together. It can only contain ChannelGroupFilterExpressions with or_group. This must be set for the top level ChannelGroupFilterExpression."}, "filter": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroupFilter", "description": "A filter on a single dimension. This cannot be set on the top level ChannelGroupFilterExpression."}, "notExpression": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpression", "description": "A filter expression to be NOT'ed (that is inverted, complemented). It can only include a dimension_or_metric_filter. This cannot be set on the top level ChannelGroupFilterExpression."}, "orGroup": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpressionList", "description": "A list of expressions to OR’ed together. It cannot contain ChannelGroupFilterExpressions with and_group or or_group."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpressionList": {"description": "A list of Channel Group filter expressions.", "id": "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpressionList", "properties": {"filterExpressions": {"description": "A list of Channel Group filter expressions.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpression"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChannelGroupFilterInListFilter": {"description": "A filter for a string dimension that matches a particular list of options. The match is case insensitive.", "id": "GoogleAnalyticsAdminV1alphaChannelGroupFilterInListFilter", "properties": {"values": {"description": "Required. The list of possible string values to match against. Must be non-empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaChannelGroupFilterStringFilter": {"description": "Filter where the field value is a String. The match is case insensitive.", "id": "GoogleAnalyticsAdminV1alphaChannelGroupFilterStringFilter", "properties": {"matchType": {"description": "Required. The match type for the string filter.", "enum": ["MATCH_TYPE_UNSPECIFIED", "EXACT", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "FULL_REGEXP", "PARTIAL_REGEXP"], "enumDescriptions": ["Default match type.", "Exact match of the string value.", "Begins with the string value.", "Ends with the string value.", "Contains the string value.", "Full regular expression match with the string value.", "Partial regular expression match with the string value."], "type": "string"}, "value": {"description": "Required. The string value to be matched against.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaConnectedSiteTag": {"description": "Configuration for a specific Connected Site Tag.", "id": "GoogleAnalyticsAdminV1alphaConnectedSiteTag", "properties": {"displayName": {"description": "Required. User-provided display name for the connected site tag. Must be less than 256 characters.", "type": "string"}, "tagId": {"description": "Required. \"Tag ID to forward events to. Also known as the Measurement ID, or the \"G-ID\" (For example: G-12345).", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaConversionEvent": {"description": "A conversion event in a Google Analytics property.", "id": "GoogleAnalyticsAdminV1alphaConversionEvent", "properties": {"countingMethod": {"description": "Optional. The method by which conversions will be counted across multiple events within a session. If this value is not provided, it will be set to `ONCE_PER_EVENT`.", "enum": ["CONVERSION_COUNTING_METHOD_UNSPECIFIED", "ONCE_PER_EVENT", "ONCE_PER_SESSION"], "enumDescriptions": ["Counting method not specified.", "Each Event instance is considered a Conversion.", "An Event instance is considered a Conversion at most once per session per user."], "type": "string"}, "createTime": {"description": "Output only. Time when this conversion event was created in the property.", "format": "google-datetime", "readOnly": true, "type": "string"}, "custom": {"description": "Output only. If set to true, this conversion event refers to a custom event. If set to false, this conversion event refers to a default event in GA. Default events typically have special meaning in GA. Default events are usually created for you by the GA system, but in some cases can be created by property admins. Custom events count towards the maximum number of custom conversion events that may be created per property.", "readOnly": true, "type": "boolean"}, "defaultConversionValue": {"$ref": "GoogleAnalyticsAdminV1alphaConversionEventDefaultConversionValue", "description": "Optional. Defines a default value/currency for a conversion event."}, "deletable": {"description": "Output only. If set, this event can currently be deleted with DeleteConversionEvent.", "readOnly": true, "type": "boolean"}, "eventName": {"description": "Immutable. The event name for this conversion event. Examples: 'click', 'purchase'", "type": "string"}, "name": {"description": "Output only. Resource name of this conversion event. Format: properties/{property}/conversionEvents/{conversion_event}", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaConversionEventDefaultConversionValue": {"description": "Defines a default value/currency for a conversion event. Both value and currency must be provided.", "id": "GoogleAnalyticsAdminV1alphaConversionEventDefaultConversionValue", "properties": {"currencyCode": {"description": "When a conversion event for this event_name has no set currency, this currency will be applied as the default. Must be in ISO 4217 currency code format. See https://en.wikipedia.org/wiki/ISO_4217 for more information.", "type": "string"}, "value": {"description": "This value will be used to populate the value for all conversions of the specified event_name where the event \"value\" parameter is unset.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaConversionValues": {"description": "Conversion value settings for a postback window for SKAdNetwork conversion value schema.", "id": "GoogleAnalyticsAdminV1alphaConversionValues", "properties": {"coarseValue": {"description": "Required. A coarse grained conversion value. This value is not guaranteed to be unique.", "enum": ["COARSE_VALUE_UNSPECIFIED", "COARSE_VALUE_LOW", "COARSE_VALUE_MEDIUM", "COARSE_VALUE_HIGH"], "enumDescriptions": ["Coarse value not specified.", "Coarse value of low.", "Coarse value of medium.", "Coarse value of high."], "type": "string"}, "displayName": {"description": "Display name of the SKAdNetwork conversion value. The max allowed display name length is 50 UTF-16 code units.", "type": "string"}, "eventMappings": {"description": "Event conditions that must be met for this Conversion Value to be achieved. The conditions in this list are ANDed together. It must have minimum of 1 entry and maximum of 3 entries, if the postback window is enabled.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaEventMapping"}, "type": "array"}, "fineValue": {"description": "The fine-grained conversion value. This is applicable only to the first postback window. Its valid values are [0,63], both inclusive. It must be set for postback window 1, and must not be set for postback window 2 & 3. This value is not guaranteed to be unique. If the configuration for the first postback window is re-used for second or third postback windows this field has no effect.", "format": "int32", "type": "integer"}, "lockEnabled": {"description": "If true, the SDK should lock to this conversion value for the current postback window.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCreateAccessBindingRequest": {"description": "Request message for CreateAccessBinding RPC.", "id": "GoogleAnalyticsAdminV1alphaCreateAccessBindingRequest", "properties": {"accessBinding": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding", "description": "Required. The access binding to create."}, "parent": {"description": "Required. Formats: - accounts/{account} - properties/{property}", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCreateConnectedSiteTagRequest": {"description": "Request message for CreateConnectedSiteTag RPC.", "id": "GoogleAnalyticsAdminV1alphaCreateConnectedSiteTagRequest", "properties": {"connectedSiteTag": {"$ref": "GoogleAnalyticsAdminV1alphaConnectedSiteTag", "description": "Required. The tag to add to the Universal Analytics property"}, "property": {"description": "The Universal Analytics property to create connected site tags for. This API does not support GA4 properties. Format: properties/{universalAnalyticsPropertyId} Example: properties/1234", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCreateConnectedSiteTagResponse": {"description": "Response message for CreateConnectedSiteTag RPC.", "id": "GoogleAnalyticsAdminV1alphaCreateConnectedSiteTagResponse", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCreateRollupPropertyRequest": {"description": "Request message for CreateRollupProperty RPC.", "id": "GoogleAnalyticsAdminV1alphaCreateRollupPropertyRequest", "properties": {"rollupProperty": {"$ref": "GoogleAnalyticsAdminV1alphaProperty", "description": "Required. The roll-up property to create."}, "sourceProperties": {"description": "Optional. The resource names of properties that will be sources to the created roll-up property.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCreateRollupPropertyResponse": {"description": "Response message for CreateRollupProperty RPC.", "id": "GoogleAnalyticsAdminV1alphaCreateRollupPropertyResponse", "properties": {"rollupProperty": {"$ref": "GoogleAnalyticsAdminV1alphaProperty", "description": "The created roll-up property."}, "rollupPropertySourceLinks": {"description": "The created roll-up property source links.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaRollupPropertySourceLink"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCustomDimension": {"description": "A definition for a CustomDimension.", "id": "GoogleAnalyticsAdminV1alphaCustomDimension", "properties": {"description": {"description": "Optional. Description for this custom dimension. Max length of 150 characters.", "type": "string"}, "disallowAdsPersonalization": {"description": "Optional. If set to true, sets this dimension as NPA and excludes it from ads personalization. This is currently only supported by user-scoped custom dimensions.", "type": "boolean"}, "displayName": {"description": "Required. Display name for this custom dimension as shown in the Analytics UI. Max length of 82 characters, alphanumeric plus space and underscore starting with a letter. Legacy system-generated display names may contain square brackets, but updates to this field will never permit square brackets.", "type": "string"}, "name": {"description": "Output only. Resource name for this CustomDimension resource. Format: properties/{property}/customDimensions/{customDimension}", "readOnly": true, "type": "string"}, "parameterName": {"description": "Required. Immutable. Tagging parameter name for this custom dimension. If this is a user-scoped dimension, then this is the user property name. If this is an event-scoped dimension, then this is the event parameter name. If this is an item-scoped dimension, then this is the parameter name found in the eCommerce items array. May only contain alphanumeric and underscore characters, starting with a letter. Max length of 24 characters for user-scoped dimensions, 40 characters for event-scoped dimensions.", "type": "string"}, "scope": {"description": "Required. Immutable. The scope of this dimension.", "enum": ["DIMENSION_SCOPE_UNSPECIFIED", "EVENT", "USER", "ITEM"], "enumDescriptions": ["Scope unknown or not specified.", "Dimension scoped to an event.", "Dimension scoped to a user.", "Dimension scoped to eCommerce items"], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaCustomMetric": {"description": "A definition for a custom metric.", "id": "GoogleAnalyticsAdminV1alphaCustomMetric", "properties": {"description": {"description": "Optional. Description for this custom dimension. Max length of 150 characters.", "type": "string"}, "displayName": {"description": "Required. Display name for this custom metric as shown in the Analytics UI. Max length of 82 characters, alphanumeric plus space and underscore starting with a letter. Legacy system-generated display names may contain square brackets, but updates to this field will never permit square brackets.", "type": "string"}, "measurementUnit": {"description": "Required. The type for the custom metric's value.", "enum": ["MEASUREMENT_UNIT_UNSPECIFIED", "STANDARD", "CURRENCY", "FEET", "METERS", "KILOMETERS", "MILES", "MILLISECONDS", "SECONDS", "MINUTES", "HOURS"], "enumDescriptions": ["MeasurementUnit unspecified or missing.", "This metric uses default units.", "This metric measures a currency.", "This metric measures feet.", "This metric measures meters.", "This metric measures kilometers.", "This metric measures miles.", "This metric measures milliseconds.", "This metric measures seconds.", "This metric measures minutes.", "This metric measures hours."], "type": "string"}, "name": {"description": "Output only. Resource name for this CustomMetric resource. Format: properties/{property}/customMetrics/{customMetric}", "readOnly": true, "type": "string"}, "parameterName": {"description": "Required. Immutable. Tagging name for this custom metric. If this is an event-scoped metric, then this is the event parameter name. May only contain alphanumeric and underscore charactes, starting with a letter. Max length of 40 characters for event-scoped metrics.", "type": "string"}, "restrictedMetricType": {"description": "Optional. Types of restricted data that this metric may contain. Required for metrics with CURRENCY measurement unit. Must be empty for metrics with a non-CURRENCY measurement unit.", "items": {"enum": ["RESTRICTED_METRIC_TYPE_UNSPECIFIED", "COST_DATA", "REVENUE_DATA"], "enumDescriptions": ["Type unknown or unspecified.", "Metric reports cost data.", "Metric reports revenue data."], "type": "string"}, "type": "array"}, "scope": {"description": "Required. Immutable. The scope of this custom metric.", "enum": ["METRIC_SCOPE_UNSPECIFIED", "EVENT"], "enumDescriptions": ["Scope unknown or not specified.", "Metric scoped to an event."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDataRedactionSettings": {"description": "Settings for client-side data redaction. Singleton resource under a Web Stream.", "id": "GoogleAnalyticsAdminV1alphaDataRedactionSettings", "properties": {"emailRedactionEnabled": {"description": "If enabled, any event parameter or user property values that look like an email will be redacted.", "type": "boolean"}, "name": {"description": "Output only. Name of this Data Redaction Settings resource. Format: properties/{property_id}/dataStreams/{data_stream}/dataRedactionSettings Example: \"properties/1000/dataStreams/2000/dataRedactionSettings\"", "readOnly": true, "type": "string"}, "queryParameterKeys": {"description": "The query parameter keys to apply redaction logic to if present in the URL. Query parameter matching is case-insensitive. Must contain at least one element if query_parameter_replacement_enabled is true. Keys cannot contain commas.", "items": {"type": "string"}, "type": "array"}, "queryParameterRedactionEnabled": {"description": "Query Parameter redaction removes the key and value portions of a query parameter if it is in the configured set of query parameters. If enabled, URL query replacement logic will be run for the Stream. Any query parameters defined in query_parameter_keys will be redacted.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDataRetentionSettings": {"description": "Settings values for data retention. This is a singleton resource.", "id": "GoogleAnalyticsAdminV1alphaDataRetentionSettings", "properties": {"eventDataRetention": {"description": "Required. The length of time that event-level data is retained.", "enum": ["RETENTION_DURATION_UNSPECIFIED", "TWO_MONTHS", "FOURTEEN_MONTHS", "TWENTY_SIX_MONTHS", "THIRTY_EIGHT_MONTHS", "FIFTY_MONTHS"], "enumDescriptions": ["Data retention time duration is not specified.", "The data retention time duration is 2 months.", "The data retention time duration is 14 months.", "The data retention time duration is 26 months. Available to 360 properties only. Available for event data only.", "The data retention time duration is 38 months. Available to 360 properties only. Available for event data only.", "The data retention time duration is 50 months. Available to 360 properties only. Available for event data only."], "type": "string"}, "name": {"description": "Output only. Resource name for this DataRetentionSetting resource. Format: properties/{property}/dataRetentionSettings", "readOnly": true, "type": "string"}, "resetUserDataOnNewActivity": {"description": "If true, reset the retention period for the user identifier with every event from that user.", "type": "boolean"}, "userDataRetention": {"description": "Required. The length of time that user-level data is retained.", "enum": ["RETENTION_DURATION_UNSPECIFIED", "TWO_MONTHS", "FOURTEEN_MONTHS", "TWENTY_SIX_MONTHS", "THIRTY_EIGHT_MONTHS", "FIFTY_MONTHS"], "enumDescriptions": ["Data retention time duration is not specified.", "The data retention time duration is 2 months.", "The data retention time duration is 14 months.", "The data retention time duration is 26 months. Available to 360 properties only. Available for event data only.", "The data retention time duration is 38 months. Available to 360 properties only. Available for event data only.", "The data retention time duration is 50 months. Available to 360 properties only. Available for event data only."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDataSharingSettings": {"description": "A resource message representing data sharing settings of a Google Analytics account.", "id": "GoogleAnalyticsAdminV1alphaDataSharingSettings", "properties": {"name": {"description": "Output only. Resource name. Format: accounts/{account}/dataSharingSettings Example: \"accounts/1000/dataSharingSettings\"", "readOnly": true, "type": "string"}, "sharingWithGoogleAnySalesEnabled": {"deprecated": true, "description": "Deprecated. This field is no longer used and always returns false.", "type": "boolean"}, "sharingWithGoogleAssignedSalesEnabled": {"description": "Allows Google access to your Google Analytics account data, including account usage and configuration data, product spending, and users associated with your Google Analytics account, so that Google can help you make the most of Google products, providing you with insights, offers, recommendations, and optimization tips across Google Analytics and other Google products for business. This field maps to the \"Recommendations for your business\" field in the Google Analytics Admin UI.", "type": "boolean"}, "sharingWithGoogleProductsEnabled": {"description": "Allows Google to use the data to improve other Google products or services. This fields maps to the \"Google products & services\" field in the Google Analytics Admin UI.", "type": "boolean"}, "sharingWithGoogleSupportEnabled": {"description": "Allows Google technical support representatives access to your Google Analytics data and account when necessary to provide service and find solutions to technical issues. This field maps to the \"Technical support\" field in the Google Analytics Admin UI.", "type": "boolean"}, "sharingWithOthersEnabled": {"description": "Enable features like predictions, modeled data, and benchmarking that can provide you with richer business insights when you contribute aggregated measurement data. The data you share (including information about the property from which it is shared) is aggregated and de-identified before being used to generate business insights. This field maps to the \"Modeling contributions & business insights\" field in the Google Analytics Admin UI.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDataStream": {"description": "A resource message representing a data stream.", "id": "GoogleAnalyticsAdminV1alphaDataStream", "properties": {"androidAppStreamData": {"$ref": "GoogleAnalyticsAdminV1alphaDataStreamAndroidAppStreamData", "description": "Data specific to Android app streams. Must be populated if type is ANDROID_APP_DATA_STREAM."}, "createTime": {"description": "Output only. Time when this stream was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Human-readable display name for the Data Stream. Required for web data streams. The max allowed display name length is 255 UTF-16 code units.", "type": "string"}, "iosAppStreamData": {"$ref": "GoogleAnalyticsAdminV1alphaDataStreamIosAppStreamData", "description": "Data specific to iOS app streams. Must be populated if type is IOS_APP_DATA_STREAM."}, "name": {"description": "Output only. Resource name of this Data Stream. Format: properties/{property_id}/dataStreams/{stream_id} Example: \"properties/1000/dataStreams/2000\"", "readOnly": true, "type": "string"}, "type": {"description": "Required. Immutable. The type of this DataStream resource.", "enum": ["DATA_STREAM_TYPE_UNSPECIFIED", "WEB_DATA_STREAM", "ANDROID_APP_DATA_STREAM", "IOS_APP_DATA_STREAM"], "enumDescriptions": ["Type unknown or not specified.", "Web data stream.", "Android app data stream.", "iOS app data stream."], "type": "string"}, "updateTime": {"description": "Output only. Time when stream payload fields were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "webStreamData": {"$ref": "GoogleAnalyticsAdminV1alphaDataStreamWebStreamData", "description": "Data specific to web streams. Must be populated if type is WEB_DATA_STREAM."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDataStreamAndroidAppStreamData": {"description": "Data specific to Android app streams.", "id": "GoogleAnalyticsAdminV1alphaDataStreamAndroidAppStreamData", "properties": {"firebaseAppId": {"description": "Output only. ID of the corresponding Android app in Firebase, if any. This ID can change if the Android app is deleted and recreated.", "readOnly": true, "type": "string"}, "packageName": {"description": "Immutable. The package name for the app being measured. Example: \"com.example.myandroidapp\"", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDataStreamIosAppStreamData": {"description": "Data specific to iOS app streams.", "id": "GoogleAnalyticsAdminV1alphaDataStreamIosAppStreamData", "properties": {"bundleId": {"description": "Required. Immutable. The Apple App Store Bundle ID for the app Example: \"com.example.myiosapp\"", "type": "string"}, "firebaseAppId": {"description": "Output only. ID of the corresponding iOS app in Firebase, if any. This ID can change if the iOS app is deleted and recreated.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDataStreamWebStreamData": {"description": "Data specific to web streams.", "id": "GoogleAnalyticsAdminV1alphaDataStreamWebStreamData", "properties": {"defaultUri": {"description": "Domain name of the web app being measured, or empty. Example: \"http://www.google.com\", \"https://www.google.com\"", "type": "string"}, "firebaseAppId": {"description": "Output only. ID of the corresponding web app in Firebase, if any. This ID can change if the web app is deleted and recreated.", "readOnly": true, "type": "string"}, "measurementId": {"description": "Output only. Analytics Measurement ID. Example: \"G-1A2BCD345E\"", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDeleteAccessBindingRequest": {"description": "Request message for DeleteAccessBinding RPC.", "id": "GoogleAnalyticsAdminV1alphaDeleteAccessBindingRequest", "properties": {"name": {"description": "Required. Formats: - accounts/{account}/accessBindings/{accessBinding} - properties/{property}/accessBindings/{accessBinding}", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDeleteConnectedSiteTagRequest": {"description": "Request message for DeleteConnectedSiteTag RPC.", "id": "GoogleAnalyticsAdminV1alphaDeleteConnectedSiteTagRequest", "properties": {"property": {"description": "The Universal Analytics property to delete connected site tags for. This API does not support GA4 properties. Format: properties/{universalAnalyticsPropertyId} Example: properties/1234", "type": "string"}, "tagId": {"description": "Tag ID to forward events to. Also known as the Measurement ID, or the \"G-ID\" (For example: G-12345).", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink": {"description": "A link between a Google Analytics property and a Display & Video 360 advertiser.", "id": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink", "properties": {"adsPersonalizationEnabled": {"description": "Enables personalized advertising features with this integration. If this field is not set on create/update, it will be defaulted to true.", "type": "boolean"}, "advertiserDisplayName": {"description": "Output only. The display name of the Display & Video 360 Advertiser.", "readOnly": true, "type": "string"}, "advertiserId": {"description": "Immutable. The Display & Video 360 Advertiser's advertiser ID.", "type": "string"}, "campaignDataSharingEnabled": {"description": "Immutable. Enables the import of campaign data from Display & Video 360 into the Google Analytics property. After link creation, this can only be updated from the Display & Video 360 product. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}, "costDataSharingEnabled": {"description": "Immutable. Enables the import of cost data from Display & Video 360 into the Google Analytics property. This can only be enabled if `campaign_data_sharing_enabled` is true. After link creation, this can only be updated from the Display & Video 360 product. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}, "name": {"description": "Output only. The resource name for this DisplayVideo360AdvertiserLink resource. Format: properties/{propertyId}/displayVideo360AdvertiserLinks/{linkId} Note: linkId is not the Display & Video 360 Advertiser ID", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLinkProposal": {"description": "A proposal for a link between a Google Analytics property and a Display & Video 360 advertiser. A proposal is converted to a DisplayVideo360AdvertiserLink once approved. Google Analytics admins approve inbound proposals while Display & Video 360 admins approve outbound proposals.", "id": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLinkProposal", "properties": {"adsPersonalizationEnabled": {"description": "Immutable. Enables personalized advertising features with this integration. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}, "advertiserDisplayName": {"description": "Output only. The display name of the Display & Video Advertiser. Only populated for proposals that originated from Display & Video 360.", "readOnly": true, "type": "string"}, "advertiserId": {"description": "Immutable. The Display & Video 360 Advertiser's advertiser ID.", "type": "string"}, "campaignDataSharingEnabled": {"description": "Immutable. Enables the import of campaign data from Display & Video 360. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}, "costDataSharingEnabled": {"description": "Immutable. Enables the import of cost data from Display & Video 360. This can only be enabled if campaign_data_sharing_enabled is enabled. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}, "linkProposalStatusDetails": {"$ref": "GoogleAnalyticsAdminV1alphaLinkProposalStatusDetails", "description": "Output only. The status information for this link proposal.", "readOnly": true}, "name": {"description": "Output only. The resource name for this DisplayVideo360AdvertiserLinkProposal resource. Format: properties/{propertyId}/displayVideo360AdvertiserLinkProposals/{proposalId} Note: proposalId is not the Display & Video 360 Advertiser ID", "readOnly": true, "type": "string"}, "validationEmail": {"description": "Input only. On a proposal being sent to Display & Video 360, this field must be set to the email address of an admin on the target advertiser. This is used to verify that the Google Analytics admin is aware of at least one admin on the Display & Video 360 Advertiser. This does not restrict approval of the proposal to a single user. Any admin on the Display & Video 360 Advertiser may approve the proposal.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaEnhancedMeasurementSettings": {"description": "Singleton resource under a web DataStream, configuring measurement of additional site interactions and content.", "id": "GoogleAnalyticsAdminV1alphaEnhancedMeasurementSettings", "properties": {"fileDownloadsEnabled": {"description": "If enabled, capture a file download event each time a link is clicked with a common document, compressed file, application, video, or audio extension.", "type": "boolean"}, "formInteractionsEnabled": {"description": "If enabled, capture a form interaction event each time a visitor interacts with a form on your website. False by default.", "type": "boolean"}, "name": {"description": "Output only. Resource name of the Enhanced Measurement Settings. Format: properties/{property_id}/dataStreams/{data_stream}/enhancedMeasurementSettings Example: \"properties/1000/dataStreams/2000/enhancedMeasurementSettings\"", "readOnly": true, "type": "string"}, "outboundClicksEnabled": {"description": "If enabled, capture an outbound click event each time a visitor clicks a link that leads them away from your domain.", "type": "boolean"}, "pageChangesEnabled": {"description": "If enabled, capture a page view event each time the website changes the browser history state.", "type": "boolean"}, "scrollsEnabled": {"description": "If enabled, capture scroll events each time a visitor gets to the bottom of a page.", "type": "boolean"}, "searchQueryParameter": {"description": "Required. URL query parameters to interpret as site search parameters. Max length is 1024 characters. Must not be empty.", "type": "string"}, "siteSearchEnabled": {"description": "If enabled, capture a view search results event each time a visitor performs a search on your site (based on a query parameter).", "type": "boolean"}, "streamEnabled": {"description": "Indicates whether Enhanced Measurement Settings will be used to automatically measure interactions and content on this web stream. Changing this value does not affect the settings themselves, but determines whether they are respected.", "type": "boolean"}, "uriQueryParameter": {"description": "Additional URL query parameters. Max length is 1024 characters.", "type": "string"}, "videoEngagementEnabled": {"description": "If enabled, capture video play, progress, and complete events as visitors view embedded videos on your site.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaEventCreateRule": {"description": "An Event Create Rule defines conditions that will trigger the creation of an entirely new event based upon matched criteria of a source event. Additional mutations of the parameters from the source event can be defined. Unlike Event Edit rules, Event Creation Rules have no defined order. They will all be run independently. Event Edit and Event Create rules can't be used to modify an event created from an Event Create rule.", "id": "GoogleAnalyticsAdminV1alphaEventCreateRule", "properties": {"destinationEvent": {"description": "Required. The name of the new event to be created. This value must: * be less than 40 characters * consist only of letters, digits or _ (underscores) * start with a letter", "type": "string"}, "eventConditions": {"description": "Required. Must have at least one condition, and can have up to 10 max. Conditions on the source event must match for this rule to be applied.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaMatchingCondition"}, "type": "array"}, "name": {"description": "Output only. Resource name for this EventCreateRule resource. Format: properties/{property}/dataStreams/{data_stream}/eventCreateRules/{event_create_rule}", "readOnly": true, "type": "string"}, "parameterMutations": {"description": "Parameter mutations define parameter behavior on the new event, and are applied in order. A maximum of 20 mutations can be applied.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaParameterMutation"}, "type": "array"}, "sourceCopyParameters": {"description": "If true, the source parameters are copied to the new event. If false, or unset, all non-internal parameters are not copied from the source event. Parameter mutations are applied after the parameters have been copied.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaEventEditRule": {"description": "An Event Edit Rule defines conditions that will trigger the creation of an entirely new event based upon matched criteria of a source event. Additional mutations of the parameters from the source event can be defined. Unlike Event Create rules, Event Edit Rules are applied in their defined order. Event Edit rules can't be used to modify an event created from an Event Create rule.", "id": "GoogleAnalyticsAdminV1alphaEventEditRule", "properties": {"displayName": {"description": "Required. The display name of this event edit rule. Maximum of 255 characters.", "type": "string"}, "eventConditions": {"description": "Required. Conditions on the source event must match for this rule to be applied. Must have at least one condition, and can have up to 10 max.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaMatchingCondition"}, "type": "array"}, "name": {"description": "Identifier. Resource name for this EventEditRule resource. Format: properties/{property}/dataStreams/{data_stream}/eventEditRules/{event_edit_rule}", "type": "string"}, "parameterMutations": {"description": "Required. Parameter mutations define parameter behavior on the new event, and are applied in order. A maximum of 20 mutations can be applied.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaParameterMutation"}, "type": "array"}, "processingOrder": {"description": "Output only. The order for which this rule will be processed. Rules with an order value lower than this will be processed before this rule, rules with an order value higher than this will be processed after this rule. New event edit rules will be assigned an order value at the end of the order. This value does not apply to event create rules.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaEventMapping": {"description": "Event setting conditions to match an event.", "id": "GoogleAnalyticsAdminV1alphaEventMapping", "properties": {"eventName": {"description": "Required. Name of the Google Analytics event. It must always be set. The max allowed display name length is 40 UTF-16 code units.", "type": "string"}, "maxEventCount": {"description": "The maximum number of times the event occurred. If not set, maximum event count won't be checked.", "format": "int64", "type": "string"}, "maxEventValue": {"description": "The maximum revenue generated due to the event. Revenue currency will be defined at the property level. If not set, maximum event value won't be checked.", "format": "double", "type": "number"}, "minEventCount": {"description": "At least one of the following four min/max values must be set. The values set will be ANDed together to qualify an event. The minimum number of times the event occurred. If not set, minimum event count won't be checked.", "format": "int64", "type": "string"}, "minEventValue": {"description": "The minimum revenue generated due to the event. Revenue currency will be defined at the property level. If not set, minimum event value won't be checked.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaExpandedDataSet": {"description": "A resource message representing an `ExpandedDataSet`.", "id": "GoogleAnalyticsAdminV1alphaExpandedDataSet", "properties": {"dataCollectionStartTime": {"description": "Output only. Time when expanded data set began (or will begin) collecing data.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the ExpandedDataSet. Max 50 chars.", "type": "string"}, "dimensionFilterExpression": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterExpression", "description": "Immutable. A logical expression of ExpandedDataSet filters applied to dimension included in the ExpandedDataSet. This filter is used to reduce the number of rows and thus the chance of encountering `other` row."}, "dimensionNames": {"description": "Immutable. The list of dimensions included in the ExpandedDataSet. See the [API Dimensions](https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema#dimensions) for the list of dimension names.", "items": {"type": "string"}, "type": "array"}, "displayName": {"description": "Required. The display name of the ExpandedDataSet. Max 200 chars.", "type": "string"}, "metricNames": {"description": "Immutable. The list of metrics included in the ExpandedDataSet. See the [API Metrics](https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema#metrics) for the list of dimension names.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Output only. The resource name for this ExpandedDataSet resource. Format: properties/{property_id}/expandedDataSets/{expanded_data_set}", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaExpandedDataSetFilter": {"description": "A specific filter for a single dimension", "id": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilter", "properties": {"fieldName": {"description": "Required. The dimension name to filter.", "type": "string"}, "inListFilter": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterInListFilter", "description": "A filter for a string dimension that matches a particular list of options."}, "stringFilter": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterStringFilter", "description": "A filter for a string-type dimension that matches a particular pattern."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterExpression": {"description": "A logical expression of EnhancedDataSet dimension filters.", "id": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterExpression", "properties": {"andGroup": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterExpressionList", "description": "A list of expressions to be AND’ed together. It must contain a ExpandedDataSetFilterExpression with either not_expression or dimension_filter. This must be set for the top level ExpandedDataSetFilterExpression."}, "filter": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilter", "description": "A filter on a single dimension. This cannot be set on the top level ExpandedDataSetFilterExpression."}, "notExpression": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterExpression", "description": "A filter expression to be NOT'ed (that is, inverted, complemented). It must include a dimension_filter. This cannot be set on the top level ExpandedDataSetFilterExpression."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterExpressionList": {"description": "A list of ExpandedDataSet filter expressions.", "id": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterExpressionList", "properties": {"filterExpressions": {"description": "A list of ExpandedDataSet filter expressions.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterExpression"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterInListFilter": {"description": "A filter for a string dimension that matches a particular list of options.", "id": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterInListFilter", "properties": {"caseSensitive": {"description": "Optional. If true, the match is case-sensitive. If false, the match is case-insensitive. Must be true.", "type": "boolean"}, "values": {"description": "Required. The list of possible string values to match against. Must be non-empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterStringFilter": {"description": "A filter for a string-type dimension that matches a particular pattern.", "id": "GoogleAnalyticsAdminV1alphaExpandedDataSetFilterStringFilter", "properties": {"caseSensitive": {"description": "Optional. If true, the match is case-sensitive. If false, the match is case-insensitive. Must be true when match_type is EXACT. Must be false when match_type is CONTAINS.", "type": "boolean"}, "matchType": {"description": "Required. The match type for the string filter.", "enum": ["MATCH_TYPE_UNSPECIFIED", "EXACT", "CONTAINS"], "enumDescriptions": ["Unspecified", "Exact match of the string value.", "Contains the string value."], "type": "string"}, "value": {"description": "Required. The string value to be matched against.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaFetchAutomatedGa4ConfigurationOptOutRequest": {"description": "Request for fetching the opt out status for the automated GA4 setup process.", "id": "GoogleAnalyticsAdminV1alphaFetchAutomatedGa4ConfigurationOptOutRequest", "properties": {"property": {"description": "Required. The UA property to get the opt out status. Note this request uses the internal property ID, not the tracking ID of the form UA-XXXXXX-YY. Format: properties/{internalWebPropertyId} Example: properties/1234", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaFetchAutomatedGa4ConfigurationOptOutResponse": {"description": "Response message for fetching the opt out status for the automated GA4 setup process.", "id": "GoogleAnalyticsAdminV1alphaFetchAutomatedGa4ConfigurationOptOutResponse", "properties": {"optOut": {"description": "The opt out status for the UA property.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaFetchConnectedGa4PropertyResponse": {"description": "Response for looking up GA4 property connected to a UA property.", "id": "GoogleAnalyticsAdminV1alphaFetchConnectedGa4PropertyResponse", "properties": {"property": {"description": "The GA4 property connected to the UA property. An empty string is returned when there is no connected GA4 property. Format: properties/{property_id} Example: properties/1234", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaFirebaseLink": {"description": "A link between a Google Analytics property and a Firebase project.", "id": "GoogleAnalyticsAdminV1alphaFirebaseLink", "properties": {"createTime": {"description": "Output only. Time when this FirebaseLink was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Example format: properties/1234/firebaseLinks/5678", "readOnly": true, "type": "string"}, "project": {"description": "Immutable. Firebase project resource name. When creating a FirebaseLink, you may provide this resource name using either a project number or project ID. Once this resource has been created, returned FirebaseLinks will always have a project_name that contains a project number. Format: 'projects/{project number}' Example: 'projects/1234'", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaGlobalSiteTag": {"description": "Read-only resource with the tag for sending data from a website to a DataStream. Only present for web DataStream resources.", "id": "GoogleAnalyticsAdminV1alphaGlobalSiteTag", "properties": {"name": {"description": "Output only. Resource name for this GlobalSiteTag resource. Format: properties/{property_id}/dataStreams/{stream_id}/globalSiteTag Example: \"properties/123/dataStreams/456/globalSiteTag\"", "readOnly": true, "type": "string"}, "snippet": {"description": "Immutable. JavaScript code snippet to be pasted as the first item into the head tag of every webpage to measure.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaGoogleAdsLink": {"description": "A link between a Google Analytics property and a Google Ads account.", "id": "GoogleAnalyticsAdminV1alphaGoogleAdsLink", "properties": {"adsPersonalizationEnabled": {"description": "Enable personalized advertising features with this integration. Automatically publish my Google Analytics audience lists and Google Analytics remarketing events/parameters to the linked Google Ads account. If this field is not set on create/update, it will be defaulted to true.", "type": "boolean"}, "canManageClients": {"description": "Output only. If true, this link is for a Google Ads manager account.", "readOnly": true, "type": "boolean"}, "createTime": {"description": "Output only. Time when this link was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creatorEmailAddress": {"description": "Output only. Email address of the user that created the link. An empty string will be returned if the email address can't be retrieved.", "readOnly": true, "type": "string"}, "customerId": {"description": "Immutable. Google Ads customer ID.", "type": "string"}, "name": {"description": "Output only. Format: properties/{propertyId}/googleAdsLinks/{googleAdsLinkId} Note: googleAdsLinkId is not the Google Ads customer ID.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when this link was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaGoogleSignalsSettings": {"description": "Settings values for Google Signals. This is a singleton resource.", "id": "GoogleAnalyticsAdminV1alphaGoogleSignalsSettings", "properties": {"consent": {"description": "Output only. Terms of Service acceptance.", "enum": ["GOOGLE_SIGNALS_CONSENT_UNSPECIFIED", "GOOGLE_SIGNALS_CONSENT_CONSENTED", "GOOGLE_SIGNALS_CONSENT_NOT_CONSENTED"], "enumDescriptions": ["Google Signals consent value defaults to GOOGLE_SIGNALS_CONSENT_UNSPECIFIED. This will be treated as GOOGLE_SIGNALS_CONSENT_NOT_CONSENTED.", "Terms of service have been accepted", "Terms of service have not been accepted"], "readOnly": true, "type": "string"}, "name": {"description": "Output only. Resource name of this setting. Format: properties/{property_id}/googleSignalsSettings Example: \"properties/1000/googleSignalsSettings\"", "readOnly": true, "type": "string"}, "state": {"description": "Status of this setting.", "enum": ["GOOGLE_SIGNALS_STATE_UNSPECIFIED", "GOOGLE_SIGNALS_ENABLED", "GOOGLE_SIGNALS_DISABLED"], "enumDescriptions": ["Google Signals status defaults to GOOGLE_SIGNALS_STATE_UNSPECIFIED to represent that the user has not made an explicit choice.", "Google Signals is enabled.", "Google Signals is disabled."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaGroupingRule": {"description": "The rules that govern how traffic is grouped into one channel.", "id": "GoogleAnalyticsAdminV1alphaGroupingRule", "properties": {"displayName": {"description": "Required. Customer defined display name for the channel.", "type": "string"}, "expression": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroupFilterExpression", "description": "Required. The Filter Expression that defines the Grouping Rule."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaKeyEvent": {"description": "A key event in a Google Analytics property.", "id": "GoogleAnalyticsAdminV1alphaKeyEvent", "properties": {"countingMethod": {"description": "Required. The method by which Key Events will be counted across multiple events within a session.", "enum": ["COUNTING_METHOD_UNSPECIFIED", "ONCE_PER_EVENT", "ONCE_PER_SESSION"], "enumDescriptions": ["Counting method not specified.", "Each Event instance is considered a Key Event.", "An Event instance is considered a Key Event at most once per session per user."], "type": "string"}, "createTime": {"description": "Output only. Time when this key event was created in the property.", "format": "google-datetime", "readOnly": true, "type": "string"}, "custom": {"description": "Output only. If set to true, this key event refers to a custom event. If set to false, this key event refers to a default event in GA. Default events typically have special meaning in GA. Default events are usually created for you by the GA system, but in some cases can be created by property admins. Custom events count towards the maximum number of custom key events that may be created per property.", "readOnly": true, "type": "boolean"}, "defaultValue": {"$ref": "GoogleAnalyticsAdminV1alphaKeyEventDefaultValue", "description": "Optional. Defines a default value/currency for a key event."}, "deletable": {"description": "Output only. If set to true, this event can be deleted.", "readOnly": true, "type": "boolean"}, "eventName": {"description": "Immutable. The event name for this key event. Examples: 'click', 'purchase'", "type": "string"}, "name": {"description": "Output only. Resource name of this key event. Format: properties/{property}/keyEvents/{key_event}", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaKeyEventDefaultValue": {"description": "Defines a default value/currency for a key event.", "id": "GoogleAnalyticsAdminV1alphaKeyEventDefaultValue", "properties": {"currencyCode": {"description": "Required. When an occurrence of this Key Event (specified by event_name) has no set currency this currency will be applied as the default. Must be in ISO 4217 currency code format. See https://en.wikipedia.org/wiki/ISO_4217 for more information.", "type": "string"}, "numericValue": {"description": "Required. This will be used to populate the \"value\" parameter for all occurrences of this Key Event (specified by event_name) where that parameter is unset.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaLinkProposalStatusDetails": {"description": "Status information for a link proposal.", "id": "GoogleAnalyticsAdminV1alphaLinkProposalStatusDetails", "properties": {"linkProposalInitiatingProduct": {"description": "Output only. The source of this proposal.", "enum": ["LINK_PROPOSAL_INITIATING_PRODUCT_UNSPECIFIED", "GOOGLE_ANALYTICS", "LINKED_PRODUCT"], "enumDescriptions": ["Unspecified product.", "This proposal was created by a user from Google Analytics.", "This proposal was created by a user from a linked product (not Google Analytics)."], "readOnly": true, "type": "string"}, "linkProposalState": {"description": "Output only. The state of this proposal.", "enum": ["LINK_PROPOSAL_STATE_UNSPECIFIED", "AWAITING_REVIEW_FROM_GOOGLE_ANALYTICS", "AWAITING_REVIEW_FROM_LINKED_PRODUCT", "WITHDRAWN", "DECLINED", "EXPIRED", "OBSOLETE"], "enumDescriptions": ["Unspecified state", "This proposal is awaiting review from a Google Analytics user. This proposal will automatically expire after some time.", "This proposal is awaiting review from a user of a linked product. This proposal will automatically expire after some time.", "This proposal has been withdrawn by an admin on the initiating product. This proposal will be automatically deleted after some time.", "This proposal has been declined by an admin on the receiving product. This proposal will be automatically deleted after some time.", "This proposal expired due to lack of response from an admin on the receiving product. This proposal will be automatically deleted after some time.", "This proposal has become obsolete because a link was directly created to the same external product resource that this proposal specifies. This proposal will be automatically deleted after some time."], "readOnly": true, "type": "string"}, "requestorEmail": {"description": "Output only. The email address of the user that proposed this linkage.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListAccessBindingsResponse": {"description": "Response message for ListAccessBindings RPC.", "id": "GoogleAnalyticsAdminV1alphaListAccessBindingsResponse", "properties": {"accessBindings": {"description": "List of AccessBindings. These will be ordered stably, but in an arbitrary order.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListAccountSummariesResponse": {"description": "Response message for ListAccountSummaries RPC.", "id": "GoogleAnalyticsAdminV1alphaListAccountSummariesResponse", "properties": {"accountSummaries": {"description": "Account summaries of all accounts the caller has access to.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccountSummary"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListAccountsResponse": {"description": "Request message for ListAccounts RPC.", "id": "GoogleAnalyticsAdminV1alphaListAccountsResponse", "properties": {"accounts": {"description": "Results that were accessible to the caller.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccount"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListAdSenseLinksResponse": {"description": "Response message for ListAdSenseLinks method.", "id": "GoogleAnalyticsAdminV1alphaListAdSenseLinksResponse", "properties": {"adsenseLinks": {"description": "List of AdSenseLinks.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAdSenseLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListAudiencesResponse": {"description": "Response message for ListAudiences RPC.", "id": "GoogleAnalyticsAdminV1alphaListAudiencesResponse", "properties": {"audiences": {"description": "List of Audiences.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAudience"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListBigQueryLinksResponse": {"description": "Response message for ListBigQueryLinks RPC", "id": "GoogleAnalyticsAdminV1alphaListBigQueryLinksResponse", "properties": {"bigqueryLinks": {"description": "List of BigQueryLinks.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaBigQueryLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListCalculatedMetricsResponse": {"description": "Response message for ListCalculatedMetrics RPC.", "id": "GoogleAnalyticsAdminV1alphaListCalculatedMetricsResponse", "properties": {"calculatedMetrics": {"description": "List of CalculatedMetrics.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaCalculatedMetric"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListChannelGroupsResponse": {"description": "Response message for ListChannelGroups RPC.", "id": "GoogleAnalyticsAdminV1alphaListChannelGroupsResponse", "properties": {"channelGroups": {"description": "List of ChannelGroup. These will be ordered stably, but in an arbitrary order.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaChannelGroup"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListConnectedSiteTagsRequest": {"description": "Request message for ListConnectedSiteTags RPC.", "id": "GoogleAnalyticsAdminV1alphaListConnectedSiteTagsRequest", "properties": {"property": {"description": "The Universal Analytics property to fetch connected site tags for. This does not work on GA4 properties. A maximum of 20 connected site tags will be returned. Example Format: `properties/1234`", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListConnectedSiteTagsResponse": {"description": "Response message for ListConnectedSiteTags RPC.", "id": "GoogleAnalyticsAdminV1alphaListConnectedSiteTagsResponse", "properties": {"connectedSiteTags": {"description": "The site tags for the Universal Analytics property. A maximum of 20 connected site tags will be returned.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaConnectedSiteTag"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListConversionEventsResponse": {"description": "Response message for ListConversionEvents RPC.", "id": "GoogleAnalyticsAdminV1alphaListConversionEventsResponse", "properties": {"conversionEvents": {"description": "The requested conversion events", "items": {"$ref": "GoogleAnalyticsAdminV1alphaConversionEvent"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListCustomDimensionsResponse": {"description": "Response message for ListCustomDimensions RPC.", "id": "GoogleAnalyticsAdminV1alphaListCustomDimensionsResponse", "properties": {"customDimensions": {"description": "List of CustomDimensions.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaCustomDimension"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListCustomMetricsResponse": {"description": "Response message for ListCustomMetrics RPC.", "id": "GoogleAnalyticsAdminV1alphaListCustomMetricsResponse", "properties": {"customMetrics": {"description": "List of CustomMetrics.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaCustomMetric"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListDataStreamsResponse": {"description": "Response message for ListDataStreams RPC.", "id": "GoogleAnalyticsAdminV1alphaListDataStreamsResponse", "properties": {"dataStreams": {"description": "List of DataStreams.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaDataStream"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListDisplayVideo360AdvertiserLinkProposalsResponse": {"description": "Response message for ListDisplayVideo360AdvertiserLinkProposals RPC.", "id": "GoogleAnalyticsAdminV1alphaListDisplayVideo360AdvertiserLinkProposalsResponse", "properties": {"displayVideo360AdvertiserLinkProposals": {"description": "List of DisplayVideo360AdvertiserLinkProposals.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLinkProposal"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListDisplayVideo360AdvertiserLinksResponse": {"description": "Response message for ListDisplayVideo360AdvertiserLinks RPC.", "id": "GoogleAnalyticsAdminV1alphaListDisplayVideo360AdvertiserLinksResponse", "properties": {"displayVideo360AdvertiserLinks": {"description": "List of DisplayVideo360AdvertiserLinks.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaDisplayVideo360AdvertiserLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListEventCreateRulesResponse": {"description": "Response message for ListEventCreateRules RPC.", "id": "GoogleAnalyticsAdminV1alphaListEventCreateRulesResponse", "properties": {"eventCreateRules": {"description": "List of EventCreateRules. These will be ordered stably, but in an arbitrary order.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaEventCreateRule"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListEventEditRulesResponse": {"description": "Response message for ListEventEditRules RPC.", "id": "GoogleAnalyticsAdminV1alphaListEventEditRulesResponse", "properties": {"eventEditRules": {"description": "List of EventEditRules. These will be ordered stably, but in an arbitrary order.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaEventEditRule"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListExpandedDataSetsResponse": {"description": "Response message for ListExpandedDataSets RPC.", "id": "GoogleAnalyticsAdminV1alphaListExpandedDataSetsResponse", "properties": {"expandedDataSets": {"description": "List of ExpandedDataSet. These will be ordered stably, but in an arbitrary order.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaExpandedDataSet"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListFirebaseLinksResponse": {"description": "Response message for ListFirebaseLinks RPC", "id": "GoogleAnalyticsAdminV1alphaListFirebaseLinksResponse", "properties": {"firebaseLinks": {"description": "List of FirebaseLinks. This will have at most one value.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaFirebaseLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. Currently, Google Analytics supports only one FirebaseLink per property, so this will never be populated.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListGoogleAdsLinksResponse": {"description": "Response message for ListGoogleAdsLinks RPC.", "id": "GoogleAnalyticsAdminV1alphaListGoogleAdsLinksResponse", "properties": {"googleAdsLinks": {"description": "List of GoogleAdsLinks.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaGoogleAdsLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListKeyEventsResponse": {"description": "Response message for ListKeyEvents RPC.", "id": "GoogleAnalyticsAdminV1alphaListKeyEventsResponse", "properties": {"keyEvents": {"description": "The requested Key Events", "items": {"$ref": "GoogleAnalyticsAdminV1alphaKeyEvent"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListMeasurementProtocolSecretsResponse": {"description": "Response message for ListMeasurementProtocolSecret RPC", "id": "GoogleAnalyticsAdminV1alphaListMeasurementProtocolSecretsResponse", "properties": {"measurementProtocolSecrets": {"description": "A list of secrets for the parent stream specified in the request.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListPropertiesResponse": {"description": "Response message for ListProperties RPC.", "id": "GoogleAnalyticsAdminV1alphaListPropertiesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "properties": {"description": "Results that matched the filter criteria and were accessible to the caller.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaProperty"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListReportingDataAnnotationsResponse": {"description": "Response message for ListReportingDataAnnotation RPC.", "id": "GoogleAnalyticsAdminV1alphaListReportingDataAnnotationsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "reportingDataAnnotations": {"description": "List of Reporting Data Annotations.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaReportingDataAnnotation"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListRollupPropertySourceLinksResponse": {"description": "Response message for ListRollupPropertySourceLinks RPC.", "id": "GoogleAnalyticsAdminV1alphaListRollupPropertySourceLinksResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "rollupPropertySourceLinks": {"description": "List of RollupPropertySourceLinks.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaRollupPropertySourceLink"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListSKAdNetworkConversionValueSchemasResponse": {"description": "Response message for ListSKAdNetworkConversionValueSchemas RPC", "id": "GoogleAnalyticsAdminV1alphaListSKAdNetworkConversionValueSchemasResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. Currently, Google Analytics supports only one SKAdNetworkConversionValueSchema per dataStream, so this will never be populated.", "type": "string"}, "skadnetworkConversionValueSchemas": {"description": "List of SKAdNetworkConversionValueSchemas. This will have at most one value.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListSearchAds360LinksResponse": {"description": "Response message for ListSearchAds360Links RPC.", "id": "GoogleAnalyticsAdminV1alphaListSearchAds360LinksResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "searchAds360Links": {"description": "List of SearchAds360Links.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaSearchAds360Link"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaListSubpropertyEventFiltersResponse": {"description": "Response message for ListSubpropertyEventFilter RPC.", "id": "GoogleAnalyticsAdminV1alphaListSubpropertyEventFiltersResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "subpropertyEventFilters": {"description": "List of subproperty event filters.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaMatchingCondition": {"description": "Defines a condition for when an Event Edit or Event Creation rule applies to an event.", "id": "GoogleAnalyticsAdminV1alphaMatchingCondition", "properties": {"comparisonType": {"description": "Required. The type of comparison to be applied to the value.", "enum": ["COMPARISON_TYPE_UNSPECIFIED", "EQUALS", "EQUALS_CASE_INSENSITIVE", "CONTAINS", "CONTAINS_CASE_INSENSITIVE", "STARTS_WITH", "STARTS_WITH_CASE_INSENSITIVE", "ENDS_WITH", "ENDS_WITH_CASE_INSENSITIVE", "GREATER_THAN", "GREATER_THAN_OR_EQUAL", "LESS_THAN", "LESS_THAN_OR_EQUAL", "REGULAR_EXPRESSION", "REGULAR_EXPRESSION_CASE_INSENSITIVE"], "enumDescriptions": ["Unknown", "Equals, case sensitive", "Equals, case insensitive", "Contains, case sensitive", "Contains, case insensitive", "Starts with, case sensitive", "Starts with, case insensitive", "Ends with, case sensitive", "Ends with, case insensitive", "Greater than", "Greater than or equal", "Less than", "Less than or equal", "regular expression. Only supported for web streams.", "regular expression, case insensitive. Only supported for web streams."], "type": "string"}, "field": {"description": "Required. The name of the field that is compared against for the condition. If 'event_name' is specified this condition will apply to the name of the event. Otherwise the condition will apply to a parameter with the specified name. This value cannot contain spaces.", "type": "string"}, "negated": {"description": "Whether or not the result of the comparison should be negated. For example, if `negated` is true, then 'equals' comparisons would function as 'not equals'.", "type": "boolean"}, "value": {"description": "Required. The value being compared against for this condition. The runtime implementation may perform type coercion of this value to evaluate this condition based on the type of the parameter value.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret": {"description": "A secret value used for sending hits to Measurement Protocol.", "id": "GoogleAnalyticsAdminV1alphaMeasurementProtocolSecret", "properties": {"displayName": {"description": "Required. Human-readable display name for this secret.", "type": "string"}, "name": {"description": "Output only. Resource name of this secret. This secret may be a child of any type of stream. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}", "readOnly": true, "type": "string"}, "secretValue": {"description": "Output only. The measurement protocol secret value. Pass this value to the api_secret field of the Measurement Protocol API when sending hits to this secret's parent property.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaNumericValue": {"description": "To represent a number.", "id": "GoogleAnalyticsAdminV1alphaNumericValue", "properties": {"doubleValue": {"description": "Double value", "format": "double", "type": "number"}, "int64Value": {"description": "Integer value", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaParameterMutation": {"description": "Defines an event parameter to mutate.", "id": "GoogleAnalyticsAdminV1alphaParameterMutation", "properties": {"parameter": {"description": "Required. The name of the parameter to mutate. This value must: * be less than 40 characters. * be unique across across all mutations within the rule * consist only of letters, digits or _ (underscores) For event edit rules, the name may also be set to 'event_name' to modify the event_name in place.", "type": "string"}, "parameterValue": {"description": "Required. The value mutation to perform. * Must be less than 100 characters. * To specify a constant value for the param, use the value's string. * To copy value from another parameter, use syntax like \"[[other_parameter]]\" For more details, see this [help center article](https://support.google.com/analytics/answer/10085872#modify-an-event&zippy=%2Cin-this-article%2Cmodify-parameters).", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaPostbackWindow": {"description": "Settings for a SKAdNetwork conversion postback window.", "id": "GoogleAnalyticsAdminV1alphaPostbackWindow", "properties": {"conversionValues": {"description": "Ordering of the repeated field will be used to prioritize the conversion value settings. Lower indexed entries are prioritized higher. The first conversion value setting that evaluates to true will be selected. It must have at least one entry if enable_postback_window_settings is set to true. It can have maximum of 128 entries.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaConversionValues"}, "type": "array"}, "postbackWindowSettingsEnabled": {"description": "If enable_postback_window_settings is true, conversion_values must be populated and will be used for determining when and how to set the Conversion Value on a client device and exporting schema to linked Ads accounts. If false, the settings are not used, but are retained in case they may be used in the future. This must always be true for postback_window_one.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaProperty": {"description": "A resource message representing a Google Analytics property.", "id": "GoogleAnalyticsAdminV1alphaProperty", "properties": {"account": {"description": "Immutable. The resource name of the parent account Format: accounts/{account_id} Example: \"accounts/123\"", "type": "string"}, "createTime": {"description": "Output only. Time when the entity was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "currencyCode": {"description": "The currency type used in reports involving monetary values. Format: https://en.wikipedia.org/wiki/ISO_4217 Examples: \"USD\", \"EUR\", \"JPY\"", "type": "string"}, "deleteTime": {"description": "Output only. If set, the time at which this property was trashed. If not set, then this property is not currently in the trash can.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. Human-readable display name for this property. The max allowed display name length is 100 UTF-16 code units.", "type": "string"}, "expireTime": {"description": "Output only. If set, the time at which this trashed property will be permanently deleted. If not set, then this property is not currently in the trash can and is not slated to be deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "industryCategory": {"description": "Industry associated with this property Example: AUTOMOTIVE, FOOD_AND_DRINK", "enum": ["INDUSTRY_CATEGORY_UNSPECIFIED", "AUTOMOTIVE", "BUSINESS_AND_INDUSTRIAL_MARKETS", "FINANCE", "HEALTHCARE", "TECHNOLOGY", "TRAVEL", "OTHER", "ARTS_AND_ENTERTAINMENT", "BEAUTY_AND_FITNESS", "BOOKS_AND_LITERATURE", "FOOD_AND_DRINK", "GAMES", "HOBBIES_AND_LEISURE", "HOME_AND_GARDEN", "INTERNET_AND_TELECOM", "LAW_AND_GOVERNMENT", "NEWS", "ONLINE_COMMUNITIES", "PEOPLE_AND_SOCIETY", "PETS_AND_ANIMALS", "REAL_ESTATE", "REFERENCE", "SCIENCE", "SPORTS", "JOBS_AND_EDUCATION", "SHOPPING"], "enumDescriptions": ["Industry category unspecified", "Automotive", "Business and industrial markets", "Finance", "Healthcare", "Technology", "Travel", "Other", "Arts and entertainment", "Beauty and fitness", "Books and literature", "Food and drink", "Games", "Hobbies and leisure", "Home and garden", "Internet and telecom", "Law and government", "News", "Online communities", "People and society", "Pets and animals", "Real estate", "Reference", "Science", "Sports", "Jobs and education", "Shopping"], "type": "string"}, "name": {"description": "Output only. Resource name of this property. Format: properties/{property_id} Example: \"properties/1000\"", "readOnly": true, "type": "string"}, "parent": {"description": "Immutable. Resource name of this property's logical parent. Note: The Property-Moving UI can be used to change the parent. Format: accounts/{account}, properties/{property} Example: \"accounts/100\", \"properties/101\"", "type": "string"}, "propertyType": {"description": "Immutable. The property type for this Property resource. When creating a property, if the type is \"PROPERTY_TYPE_UNSPECIFIED\", then \"ORDINARY_PROPERTY\" will be implied.", "enum": ["PROPERTY_TYPE_UNSPECIFIED", "PROPERTY_TYPE_ORDINARY", "PROPERTY_TYPE_SUBPROPERTY", "PROPERTY_TYPE_ROLLUP"], "enumDescriptions": ["Unknown or unspecified property type", "Ordinary Google Analytics property", "Google Analytics subproperty", "Google Analytics rollup property"], "type": "string"}, "serviceLevel": {"description": "Output only. The Google Analytics service level that applies to this property.", "enum": ["SERVICE_LEVEL_UNSPECIFIED", "GOOGLE_ANALYTICS_STANDARD", "GOOGLE_ANALYTICS_360"], "enumDescriptions": ["Service level not specified or invalid.", "The standard version of Google Analytics.", "The paid, premium version of Google Analytics."], "readOnly": true, "type": "string"}, "timeZone": {"description": "Required. Reporting Time Zone, used as the day boundary for reports, regardless of where the data originates. If the time zone honors DST, Analytics will automatically adjust for the changes. NOTE: Changing the time zone only affects data going forward, and is not applied retroactively. Format: https://www.iana.org/time-zones Example: \"America/Los_Angeles\"", "type": "string"}, "updateTime": {"description": "Output only. Time when entity payload fields were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaPropertySummary": {"description": "A virtual resource representing metadata for a Google Analytics property.", "id": "GoogleAnalyticsAdminV1alphaPropertySummary", "properties": {"displayName": {"description": "Display name for the property referred to in this property summary.", "type": "string"}, "parent": {"description": "Resource name of this property's logical parent. Note: The Property-Moving UI can be used to change the parent. Format: accounts/{account}, properties/{property} Example: \"accounts/100\", \"properties/200\"", "type": "string"}, "property": {"description": "Resource name of property referred to by this property summary Format: properties/{property_id} Example: \"properties/1000\"", "type": "string"}, "propertyType": {"description": "The property's property type.", "enum": ["PROPERTY_TYPE_UNSPECIFIED", "PROPERTY_TYPE_ORDINARY", "PROPERTY_TYPE_SUBPROPERTY", "PROPERTY_TYPE_ROLLUP"], "enumDescriptions": ["Unknown or unspecified property type", "Ordinary Google Analytics property", "Google Analytics subproperty", "Google Analytics rollup property"], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaProvisionAccountTicketRequest": {"description": "Request message for ProvisionAccountTicket RPC.", "id": "GoogleAnalyticsAdminV1alphaProvisionAccountTicketRequest", "properties": {"account": {"$ref": "GoogleAnalyticsAdminV1alphaAccount", "description": "The account to create."}, "redirectUri": {"description": "Redirect URI where the user will be sent after accepting Terms of Service. Must be configured in Cloud Console as a Redirect URI.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaProvisionAccountTicketResponse": {"description": "Response message for ProvisionAccountTicket RPC.", "id": "GoogleAnalyticsAdminV1alphaProvisionAccountTicketResponse", "properties": {"accountTicketId": {"description": "The param to be passed in the ToS link.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaProvisionSubpropertyRequest": {"description": "Request message for CreateSubproperty RPC.", "id": "GoogleAnalyticsAdminV1alphaProvisionSubpropertyRequest", "properties": {"subproperty": {"$ref": "GoogleAnalyticsAdminV1alphaProperty", "description": "Required. The subproperty to create."}, "subpropertyEventFilter": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter", "description": "Optional. The subproperty event filter to create on an ordinary property."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaProvisionSubpropertyResponse": {"description": "Response message for ProvisionSubproperty RPC.", "id": "GoogleAnalyticsAdminV1alphaProvisionSubpropertyResponse", "properties": {"subproperty": {"$ref": "GoogleAnalyticsAdminV1alphaProperty", "description": "The created subproperty."}, "subpropertyEventFilter": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter", "description": "The created subproperty event filter."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaReorderEventEditRulesRequest": {"description": "Request message for ReorderEventEditRules RPC.", "id": "GoogleAnalyticsAdminV1alphaReorderEventEditRulesRequest", "properties": {"eventEditRules": {"description": "Required. EventEditRule resource names for the specified data stream, in the needed processing order. All EventEditRules for the stream must be present in the list.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaReportingDataAnnotation": {"description": "A Reporting Data Annotation is a comment connected to certain dates for reporting data.", "id": "GoogleAnalyticsAdminV1alphaReportingDataAnnotation", "properties": {"annotationDate": {"$ref": "GoogleTypeDate", "description": "If set, the Reporting Data Annotation is for a specific date represented by this field. The date must be a valid date with year, month and day set. The date may be in the past, present, or future."}, "annotationDateRange": {"$ref": "GoogleAnalyticsAdminV1alphaReportingDataAnnotationDateRange", "description": "If set, the Reporting Data Annotation is for a range of dates represented by this field."}, "color": {"description": "Required. The color used for display of this Reporting Data Annotation.", "enum": ["COLOR_UNSPECIFIED", "PURPLE", "BROWN", "BLUE", "GREEN", "RED", "CYAN", "ORANGE"], "enumDescriptions": ["Color unknown or not specified.", "Purple color.", "Brown color.", "Blue color.", "Green color.", "Red color.", "Cyan color.", "Orange color. (Only used for system-generated annotations)"], "type": "string"}, "description": {"description": "Optional. Description for this Reporting Data Annotation.", "type": "string"}, "name": {"description": "Required. Identifier. Resource name of this Reporting Data Annotation. Format: 'properties/{property_id}/reportingDataAnnotations/{reporting_data_annotation}' Format: 'properties/123/reportingDataAnnotations/456'", "type": "string"}, "systemGenerated": {"description": "Output only. If true, this annotation was generated by the Google Analytics system. System-generated annotations cannot be updated or deleted.", "readOnly": true, "type": "boolean"}, "title": {"description": "Required. Human-readable title for this Reporting Data Annotation.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaReportingDataAnnotationDateRange": {"description": "Represents a Reporting Data Annotation's date range, both start and end dates are inclusive. Time zones are based on the parent property.", "id": "GoogleAnalyticsAdminV1alphaReportingDataAnnotationDateRange", "properties": {"endDate": {"$ref": "GoogleTypeDate", "description": "Required. The end date for this range. Must be a valid date with year, month, and day set. This date must be greater than or equal to the start date."}, "startDate": {"$ref": "GoogleTypeDate", "description": "Required. The start date for this range. Must be a valid date with year, month, and day set. The date may be in the past, present, or future."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaRollupPropertySourceLink": {"description": "A link that references a source property under the parent rollup property.", "id": "GoogleAnalyticsAdminV1alphaRollupPropertySourceLink", "properties": {"name": {"description": "Output only. Resource name of this RollupPropertySourceLink. Format: 'properties/{property_id}/rollupPropertySourceLinks/{rollup_property_source_link}' Format: 'properties/123/rollupPropertySourceLinks/456'", "readOnly": true, "type": "string"}, "sourceProperty": {"description": "Immutable. Resource name of the source property. Format: properties/{property_id} Example: \"properties/789\"", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaRunAccessReportRequest": {"description": "The request for a Data Access Record Report.", "id": "GoogleAnalyticsAdminV1alphaRunAccessReportRequest", "properties": {"dateRanges": {"description": "Date ranges of access records to read. If multiple date ranges are requested, each response row will contain a zero based date range index. If two date ranges overlap, the access records for the overlapping days is included in the response rows for both date ranges. Requests are allowed up to 2 date ranges.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessDateRange"}, "type": "array"}, "dimensionFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAccessFilterExpression", "description": "Dimension filters let you restrict report response to specific dimension values which match the filter. For example, filtering on access records of a single user. To learn more, see [Fundamentals of Dimension Filters](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#dimension_filters) for examples. Metrics cannot be used in this filter."}, "dimensions": {"description": "The dimensions requested and displayed in the response. Requests are allowed up to 9 dimensions.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessDimension"}, "type": "array"}, "expandGroups": {"description": "Optional. Decides whether to return the users within user groups. This field works only when include_all_users is set to true. If true, it will return all users with access to the specified property or account. If false, only the users with direct access will be returned.", "type": "boolean"}, "includeAllUsers": {"description": "Optional. Determines whether to include users who have never made an API call in the response. If true, all users with access to the specified property or account are included in the response, regardless of whether they have made an API call or not. If false, only the users who have made an API call will be included.", "type": "boolean"}, "limit": {"description": "The number of rows to return. If unspecified, 10,000 rows are returned. The API returns a maximum of 100,000 rows per request, no matter how many you ask for. `limit` must be positive. The API may return fewer rows than the requested `limit`, if there aren't as many remaining rows as the `limit`. For instance, there are fewer than 300 possible values for the dimension `country`, so when reporting on only `country`, you can't get more than 300 rows, even if you set `limit` to a higher value. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int64", "type": "string"}, "metricFilter": {"$ref": "GoogleAnalyticsAdminV1alphaAccessFilterExpression", "description": "Metric filters allow you to restrict report response to specific metric values which match the filter. Metric filters are applied after aggregating the report's rows, similar to SQL having-clause. Dimensions cannot be used in this filter."}, "metrics": {"description": "The metrics requested and displayed in the response. Requests are allowed up to 10 metrics.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessMetric"}, "type": "array"}, "offset": {"description": "The row count of the start row. The first row is counted as row 0. If offset is unspecified, it is treated as 0. If offset is zero, then this method will return the first page of results with `limit` entries. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int64", "type": "string"}, "orderBys": {"description": "Specifies how rows are ordered in the response.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessOrderBy"}, "type": "array"}, "returnEntityQuota": {"description": "Toggles whether to return the current state of this Analytics Property's quota. Quota is returned in [AccessQuota](#AccessQuota). For account-level requests, this field must be false.", "type": "boolean"}, "timeZone": {"description": "This request's time zone if specified. If unspecified, the property's time zone is used. The request's time zone is used to interpret the start & end dates of the report. Formatted as strings from the IANA Time Zone database (https://www.iana.org/time-zones); for example \"America/New_York\" or \"Asia/Tokyo\".", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaRunAccessReportResponse": {"description": "The customized Data Access Record Report response.", "id": "GoogleAnalyticsAdminV1alphaRunAccessReportResponse", "properties": {"dimensionHeaders": {"description": "The header for a column in the report that corresponds to a specific dimension. The number of DimensionHeaders and ordering of DimensionHeaders matches the dimensions present in rows.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessDimensionHeader"}, "type": "array"}, "metricHeaders": {"description": "The header for a column in the report that corresponds to a specific metric. The number of MetricHeaders and ordering of MetricHeaders matches the metrics present in rows.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessMetricHeader"}, "type": "array"}, "quota": {"$ref": "GoogleAnalyticsAdminV1alphaAccessQuota", "description": "The quota state for this Analytics property including this request. This field doesn't work with account-level requests."}, "rowCount": {"description": "The total number of rows in the query result. `rowCount` is independent of the number of rows returned in the response, the `limit` request parameter, and the `offset` request parameter. For example if a query returns 175 rows and includes `limit` of 50 in the API request, the response will contain `rowCount` of 175 but only 50 rows. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int32", "type": "integer"}, "rows": {"description": "Rows of dimension value combinations and metric values in the report.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaAccessRow"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema": {"description": "SKAdNetwork conversion value schema of an iOS stream.", "id": "GoogleAnalyticsAdminV1alphaSKAdNetworkConversionValueSchema", "properties": {"applyConversionValues": {"description": "If enabled, the GA SDK will set conversion values using this schema definition, and schema will be exported to any Google Ads accounts linked to this property. If disabled, the GA SDK will not automatically set conversion values, and also the schema will not be exported to Ads.", "type": "boolean"}, "name": {"description": "Output only. Resource name of the schema. This will be child of ONLY an iOS stream, and there can be at most one such child under an iOS stream. Format: properties/{property}/dataStreams/{dataStream}/sKAdNetworkConversionValueSchema", "readOnly": true, "type": "string"}, "postbackWindowOne": {"$ref": "GoogleAnalyticsAdminV1alphaPostbackWindow", "description": "Required. The conversion value settings for the first postback window. These differ from values for postback window two and three in that they contain a \"Fine\" grained conversion value (a numeric value). Conversion values for this postback window must be set. The other windows are optional and may inherit this window's settings if unset or disabled."}, "postbackWindowThree": {"$ref": "GoogleAnalyticsAdminV1alphaPostbackWindow", "description": "The conversion value settings for the third postback window. This field should only be set if the user chose to define different conversion values for this postback window. It is allowed to configure window 3 without setting window 2. In case window 1 & 2 settings are set and enable_postback_window_settings for this postback window is set to false, the schema will inherit settings from postback_window_two."}, "postbackWindowTwo": {"$ref": "GoogleAnalyticsAdminV1alphaPostbackWindow", "description": "The conversion value settings for the second postback window. This field should only be configured if there is a need to define different conversion values for this postback window. If enable_postback_window_settings is set to false for this postback window, the values from postback_window_one will be used."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSearchAds360Link": {"description": "A link between a Google Analytics property and a Search Ads 360 entity.", "id": "GoogleAnalyticsAdminV1alphaSearchAds360Link", "properties": {"adsPersonalizationEnabled": {"description": "Enables personalized advertising features with this integration. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}, "advertiserDisplayName": {"description": "Output only. The display name of the Search Ads 360 Advertiser. Allows users to easily identify the linked resource.", "readOnly": true, "type": "string"}, "advertiserId": {"description": "Immutable. This field represents the Advertiser ID of the Search Ads 360 Advertiser. that has been linked.", "type": "string"}, "campaignDataSharingEnabled": {"description": "Immutable. Enables the import of campaign data from Search Ads 360 into the Google Analytics property. After link creation, this can only be updated from the Search Ads 360 product. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}, "costDataSharingEnabled": {"description": "Immutable. Enables the import of cost data from Search Ads 360 to the Google Analytics property. This can only be enabled if campaign_data_sharing_enabled is enabled. After link creation, this can only be updated from the Search Ads 360 product. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}, "name": {"description": "Output only. The resource name for this SearchAds360Link resource. Format: properties/{propertyId}/searchAds360Links/{linkId} Note: linkId is not the Search Ads 360 advertiser ID", "readOnly": true, "type": "string"}, "siteStatsSharingEnabled": {"description": "Enables export of site stats with this integration. If this field is not set on create, it will be defaulted to true.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSearchChangeHistoryEventsRequest": {"description": "Request message for SearchChangeHistoryEvents RPC.", "id": "GoogleAnalyticsAdminV1alphaSearchChangeHistoryEventsRequest", "properties": {"action": {"description": "Optional. If set, only return changes that match one or more of these types of actions.", "items": {"enum": ["ACTION_TYPE_UNSPECIFIED", "CREATED", "UPDATED", "DELETED"], "enumDescriptions": ["Action type unknown or not specified.", "Resource was created in this change.", "Resource was updated in this change.", "Resource was deleted in this change."], "type": "string"}, "type": "array"}, "actorEmail": {"description": "Optional. If set, only return changes if they are made by a user in this list.", "items": {"type": "string"}, "type": "array"}, "earliestChangeTime": {"description": "Optional. If set, only return changes made after this time (inclusive).", "format": "google-datetime", "type": "string"}, "latestChangeTime": {"description": "Optional. If set, only return changes made before this time (inclusive).", "format": "google-datetime", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of ChangeHistoryEvent items to return. If unspecified, at most 50 items will be returned. The maximum value is 200 (higher values will be coerced to the maximum). Note that the service may return a page with fewer items than this value specifies (potentially even zero), and that there still may be additional pages. If you want a particular number of items, you'll need to continue requesting additional pages using `page_token` until you get the needed number.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `SearchChangeHistoryEvents` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `SearchChangeHistoryEvents` must match the call that provided the page token.", "type": "string"}, "property": {"description": "Optional. Resource name for a child property. If set, only return changes made to this property or its child resources. Format: properties/{propertyId} Example: `properties/100`", "type": "string"}, "resourceType": {"description": "Optional. If set, only return changes if they are for a resource that matches at least one of these types.", "items": {"enum": ["CHANGE_HISTORY_RESOURCE_TYPE_UNSPECIFIED", "ACCOUNT", "PROPERTY", "FIREBASE_LINK", "GOOGLE_ADS_LINK", "GOOGLE_SIGNALS_SETTINGS", "CONVERSION_EVENT", "MEASUREMENT_PROTOCOL_SECRET", "CUSTOM_DIMENSION", "CUSTOM_METRIC", "DATA_RETENTION_SETTINGS", "DISPLAY_VIDEO_360_ADVERTISER_LINK", "DISPLAY_VIDEO_360_ADVERTISER_LINK_PROPOSAL", "SEARCH_ADS_360_LINK", "DATA_STREAM", "ATTRIBUTION_SETTINGS", "EXPANDED_DATA_SET", "CHANNEL_GROUP", "BIGQUERY_LINK", "ENHANCED_MEASUREMENT_SETTINGS", "DATA_REDACTION_SETTINGS", "SKADNETWORK_CONVERSION_VALUE_SCHEMA", "ADSENSE_LINK", "AUDIENCE", "EVENT_CREATE_RULE", "KEY_EVENT", "CALCULATED_METRIC", "REPORTING_DATA_ANNOTATION"], "enumDescriptions": ["Resource type unknown or not specified.", "Account resource", "Property resource", "FirebaseLink resource", "GoogleAdsLink resource", "GoogleSignalsSettings resource", "ConversionEvent resource", "MeasurementProtocolSecret resource", "CustomDimension resource", "CustomMetric resource", "DataRetentionSettings resource", "DisplayVideo360AdvertiserLink resource", "DisplayVideo360AdvertiserLinkProposal resource", "SearchAds360Link resource", "DataStream resource", "AttributionSettings resource", "ExpandedDataSet resource", "ChannelGroup resource", "BigQuery link resource", "EnhancedMeasurementSettings resource", "DataRedactionSettings resource", "SKAdNetworkConversionValueSchema resource", "AdSenseLink resource", "Audience resource", "EventCreateRule resource", "KeyEvent resource", "CalculatedMetric resource", "ReportingDataAnnotation resource"], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSearchChangeHistoryEventsResponse": {"description": "Response message for SearchAccounts RPC.", "id": "GoogleAnalyticsAdminV1alphaSearchChangeHistoryEventsResponse", "properties": {"changeHistoryEvents": {"description": "Results that were accessible to the caller.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaChangeHistoryEvent"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSetAutomatedGa4ConfigurationOptOutRequest": {"description": "Request for setting the opt out status for the automated GA4 setup process.", "id": "GoogleAnalyticsAdminV1alphaSetAutomatedGa4ConfigurationOptOutRequest", "properties": {"optOut": {"description": "The status to set.", "type": "boolean"}, "property": {"description": "Required. The UA property to set the opt out status. Note this request uses the internal property ID, not the tracking ID of the form UA-XXXXXX-YY. Format: properties/{internalWebPropertyId} Example: properties/1234", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSetAutomatedGa4ConfigurationOptOutResponse": {"description": "Response message for setting the opt out status for the automated GA4 setup process.", "id": "GoogleAnalyticsAdminV1alphaSetAutomatedGa4ConfigurationOptOutResponse", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter": {"description": "A resource message representing a Google Analytics subproperty event filter.", "id": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilter", "properties": {"applyToProperty": {"description": "Immutable. Resource name of the Subproperty that uses this filter.", "type": "string"}, "filterClauses": {"description": "Required. Unordered list. Filter clauses that define the SubpropertyEventFilter. All clauses are AND'ed together to determine what data is sent to the subproperty.", "items": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterClause"}, "type": "array"}, "name": {"description": "Output only. Format: properties/{ordinary_property_id}/subpropertyEventFilters/{sub_property_event_filter} Example: properties/1234/subpropertyEventFilters/5678", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterClause": {"description": "A clause for defining a filter. A filter may be inclusive (events satisfying the filter clause are included in the subproperty's data) or exclusive (events satisfying the filter clause are excluded from the subproperty's data).", "id": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterClause", "properties": {"filterClauseType": {"description": "Required. The type for the filter clause.", "enum": ["FILTER_CLAUSE_TYPE_UNSPECIFIED", "INCLUDE", "EXCLUDE"], "enumDescriptions": ["Filter clause type unknown or not specified.", "Events will be included in the Sub property if the filter clause is met.", "Events will be excluded from the Sub property if the filter clause is met."], "type": "string"}, "filterExpression": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterExpression", "description": "Required. The logical expression for what events are sent to the subproperty."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterCondition": {"description": "A specific filter expression", "id": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterCondition", "properties": {"fieldName": {"description": "Required. The field that is being filtered.", "type": "string"}, "nullFilter": {"description": "A filter for null values.", "type": "boolean"}, "stringFilter": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterConditionStringFilter", "description": "A filter for a string-type dimension that matches a particular pattern."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterConditionStringFilter": {"description": "A filter for a string-type dimension that matches a particular pattern.", "id": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterConditionStringFilter", "properties": {"caseSensitive": {"description": "Optional. If true, the string value is case sensitive. If false, the match is case-insensitive.", "type": "boolean"}, "matchType": {"description": "Required. The match type for the string filter.", "enum": ["MATCH_TYPE_UNSPECIFIED", "EXACT", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "FULL_REGEXP", "PARTIAL_REGEXP"], "enumDescriptions": ["Match type unknown or not specified.", "Exact match of the string value.", "Begins with the string value.", "Ends with the string value.", "Contains the string value.", "Full regular expression matches with the string value.", "Partial regular expression matches with the string value."], "type": "string"}, "value": {"description": "Required. The string value used for the matching.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterExpression": {"description": "A logical expression of Subproperty event filters.", "id": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterExpression", "properties": {"filterCondition": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterCondition", "description": "Creates a filter that matches a specific event. This cannot be set on the top level SubpropertyEventFilterExpression."}, "notExpression": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterExpression", "description": "A filter expression to be NOT'ed (inverted, complemented). It can only include a filter. This cannot be set on the top level SubpropertyEventFilterExpression."}, "orGroup": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterExpressionList", "description": "A list of expressions to OR’ed together. Must only contain not_expression or filter_condition expressions."}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterExpressionList": {"description": "A list of Subproperty event filter expressions.", "id": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterExpressionList", "properties": {"filterExpressions": {"description": "Required. Unordered list. A list of Subproperty event filter expressions", "items": {"$ref": "GoogleAnalyticsAdminV1alphaSubpropertyEventFilterExpression"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1alphaUpdateAccessBindingRequest": {"description": "Request message for UpdateAccessBinding RPC.", "id": "GoogleAnalyticsAdminV1alphaUpdateAccessBindingRequest", "properties": {"accessBinding": {"$ref": "GoogleAnalyticsAdminV1alphaAccessBinding", "description": "Required. The access binding to update."}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleTypeDate": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "GoogleTypeDate", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}}, "servicePath": "", "title": "Google Analytics Admin API", "version": "v1alpha", "version_module": true}