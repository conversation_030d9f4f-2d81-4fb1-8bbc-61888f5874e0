{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/bigquery": {"description": "View and manage your data in Google BigQuery and see the email address for your Google Account"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://bigqueryreservation.googleapis.com/", "batchPath": "batch", "canonicalName": "BigQuery Reservation", "description": "A service to modify your BigQuery flat-rate reservations.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/bigquery/", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.europe-west3.rep.googleapis.com/", "location": "europe-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.europe-west8.rep.googleapis.com/", "location": "europe-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.europe-west9.rep.googleapis.com/", "location": "europe-west9"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.me-central2.rep.googleapis.com/", "location": "me-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-central1.rep.googleapis.com/", "location": "us-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-central2.rep.googleapis.com/", "location": "us-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-east1.rep.googleapis.com/", "location": "us-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-east4.rep.googleapis.com/", "location": "us-east4"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-east5.rep.googleapis.com/", "location": "us-east5"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-east7.rep.googleapis.com/", "location": "us-east7"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-south1.rep.googleapis.com/", "location": "us-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-west1.rep.googleapis.com/", "location": "us-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-west2.rep.googleapis.com/", "location": "us-west2"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-west3.rep.googleapis.com/", "location": "us-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-west4.rep.googleapis.com/", "location": "us-west4"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigqueryreservation.us-west8.rep.googleapis.com/", "location": "us-west8"}], "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "bigqueryreservation:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://bigqueryreservation.mtls.googleapis.com/", "name": "bigqueryreservation", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"getBiReservation": {"description": "Retrieves a BI reservation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/biReservation", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.getBiReservation", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the requested reservation, for example: `projects/{project_id}/locations/{location_id}/biReservation`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/biReservation$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BiReservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "searchAllAssignments": {"description": "Looks up assignments for a specified resource for a particular region. If the request is about a project: 1. Assignments created on the project will be returned if they exist. 2. Otherwise assignments created on the closest ancestor will be returned. 3. Assignments for different JobTypes will all be returned. The same logic applies if the request is about a folder. If the request is about an organization, then assignments created on the organization will be returned (organization doesn't have ancestors). Comparing to ListAssignments, there are some behavior differences: 1. permission on the assignee will be verified in this API. 2. Hierarchy lookup (project->folder->organization) happens in this API. 3. Parent here is `projects/*/locations/*`, instead of `projects/*/locations/*reservations/*`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:searchAllAssignments", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.searchAllAssignments", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of items to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name with location (project name could be the wildcard '-'), e.g.: `projects/-/locations/US`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "query": {"description": "Please specify resource name as assignee in the query. Examples: * `assignee=projects/myproject` * `assignee=folders/123` * `assignee=organizations/456`", "location": "query", "type": "string"}}, "path": "v1/{+parent}:searchAllAssignments", "response": {"$ref": "SearchAllAssignmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "searchAssignments": {"deprecated": true, "description": "Deprecated: Looks up assignments for a specified resource for a particular region. If the request is about a project: 1. Assignments created on the project will be returned if they exist. 2. Otherwise assignments created on the closest ancestor will be returned. 3. Assignments for different JobTypes will all be returned. The same logic applies if the request is about a folder. If the request is about an organization, then assignments created on the organization will be returned (organization doesn't have ancestors). Comparing to ListAssignments, there are some behavior differences: 1. permission on the assignee will be verified in this API. 2. Hierarchy lookup (project->folder->organization) happens in this API. 3. Parent here is `projects/*/locations/*`, instead of `projects/*/locations/*reservations/*`. **Note** \"-\" cannot be used for projects nor locations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:searchAssignments", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.searchAssignments", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of items to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the admin project(containing project and location), e.g.: `projects/myproject/locations/US`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "query": {"description": "Please specify resource name as assignee in the query. Examples: * `assignee=projects/myproject` * `assignee=folders/123` * `assignee=organizations/456`", "location": "query", "type": "string"}}, "path": "v1/{+parent}:searchAssignments", "response": {"$ref": "SearchAssignmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "updateBiReservation": {"description": "Updates a BI reservation. Only fields specified in the `field_mask` are updated. A singleton BI reservation always exists with default size 0. In order to reserve BI capacity it needs to be updated to an amount greater than 0. In order to release BI capacity reservation size must be set to 0.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/biReservation", "httpMethod": "PATCH", "id": "bigqueryreservation.projects.locations.updateBiReservation", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the singleton BI reservation. Reservation names have the form `projects/{project_id}/locations/{location_id}/biReservation`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/biReservation$", "required": true, "type": "string"}, "updateMask": {"description": "A list of fields to be updated in this request.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "BiReservation"}, "response": {"$ref": "BiReservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"capacityCommitments": {"methods": {"create": {"description": "Creates a new capacity commitment resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.capacityCommitments.create", "parameterOrder": ["parent"], "parameters": {"capacityCommitmentId": {"description": "The optional capacity commitment ID. Capacity commitment name will be generated automatically if this field is empty. This field must only contain lower case alphanumeric characters or dashes. The first and last character cannot be a dash. Max length is 64 characters. NOTE: this ID won't be kept if the capacity commitment is split or merged.", "location": "query", "type": "string"}, "enforceSingleAdminProjectPerOrg": {"description": "If true, fail the request if another project in the organization has a capacity commitment.", "location": "query", "type": "boolean"}, "parent": {"description": "Required. Resource name of the parent reservation. E.g., `projects/myproject/locations/US`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/capacityCommitments", "request": {"$ref": "CapacityCommitment"}, "response": {"$ref": "CapacityCommitment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a capacity commitment. Attempting to delete capacity commitment before its commitment_end_time will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments/{capacityCommitmentsId}", "httpMethod": "DELETE", "id": "bigqueryreservation.projects.locations.capacityCommitments.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Can be used to force delete commitments even if assignments exist. Deleting commitments with assignments may cause queries to fail if they no longer have access to slots.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Resource name of the capacity commitment to delete. E.g., `projects/myproject/locations/US/capacityCommitments/123`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/capacityCommitments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns information about the capacity commitment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments/{capacityCommitmentsId}", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.capacityCommitments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the capacity commitment to retrieve. E.g., `projects/myproject/locations/US/capacityCommitments/123`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/capacityCommitments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "CapacityCommitment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the capacity commitments for the admin project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.capacityCommitments.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of items to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the parent reservation. E.g., `projects/myproject/locations/US`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/capacityCommitments", "response": {"$ref": "ListCapacityCommitmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "merge": {"description": "Merges capacity commitments of the same plan into a single commitment. The resulting capacity commitment has the greater commitment_end_time out of the to-be-merged capacity commitments. Attempting to merge capacity commitments of different plan will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments:merge", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.capacityCommitments.merge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Parent resource that identifies admin project and location e.g., `projects/myproject/locations/us`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/capacityCommitments:merge", "request": {"$ref": "MergeCapacityCommitmentsRequest"}, "response": {"$ref": "CapacityCommitment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing capacity commitment. Only `plan` and `renewal_plan` fields can be updated. Plan can only be changed to a plan of a longer commitment period. Attempting to change to a plan with shorter commitment period will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments/{capacityCommitmentsId}", "httpMethod": "PATCH", "id": "bigqueryreservation.projects.locations.capacityCommitments.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the capacity commitment, e.g., `projects/myproject/locations/US/capacityCommitments/123` The commitment_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/capacityCommitments/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Standard field mask for the set of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "CapacityCommitment"}, "response": {"$ref": "CapacityCommitment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "split": {"description": "Splits capacity commitment to two commitments of the same plan and `commitment_end_time`. A common use case is to enable downgrading commitments. For example, in order to downgrade from 10000 slots to 8000, you might split a 10000 capacity commitment into commitments of 2000 and 8000. Then, you delete the first one after the commitment end time passes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments/{capacityCommitmentsId}:split", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.capacityCommitments.split", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name e.g.,: `projects/myproject/locations/US/capacityCommitments/123`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/capacityCommitments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:split", "request": {"$ref": "SplitCapacityCommitmentRequest"}, "response": {"$ref": "SplitCapacityCommitmentResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}, "reservations": {"methods": {"create": {"description": "Creates a new reservation resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.reservations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Project, location. E.g., `projects/myproject/locations/US`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "reservationId": {"description": "The reservation ID. It must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/reservations", "request": {"$ref": "Reservation"}, "response": {"$ref": "Reservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a reservation. Returns `google.rpc.Code.FAILED_PRECONDITION` when reservation has assignments.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}", "httpMethod": "DELETE", "id": "bigqueryreservation.projects.locations.reservations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the reservation to retrieve. E.g., `projects/myproject/locations/US/reservations/team1-prod`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "failoverReservation": {"description": "Fail over a reservation to the secondary location. The operation should be done in the current secondary location, which will be promoted to the new primary location for the reservation. Attempting to failover a reservation in the current primary location will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}:failoverReservation", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.reservations.failoverReservation", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the reservation to failover. E.g., `projects/myproject/locations/US/reservations/team1-prod`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:failoverReservation", "request": {"$ref": "FailoverReservationRequest"}, "response": {"$ref": "Reservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns information about the reservation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.reservations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the reservation to retrieve. E.g., `projects/myproject/locations/US/reservations/team1-prod`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Reservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. May return: * A`NOT_FOUND` error if the resource doesn't exist or you don't have the permission to view it. * An empty policy if the resource exists but doesn't have a set policy. Supported resources are: - Reservations - ReservationAssignments To call this method, you must have the following Google IAM permissions: - `bigqueryreservation.reservations.getIamPolicy` to get policies on reservations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}:getIamPolicy", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.reservations.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the reservations for the project in the specified location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.reservations.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of items to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name containing project and location, e.g.: `projects/myproject/locations/US`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/reservations", "response": {"$ref": "ListReservationsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing reservation resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}", "httpMethod": "PATCH", "id": "bigqueryreservation.projects.locations.reservations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the reservation, e.g., `projects/*/locations/*/reservations/team1-prod`. The reservation_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Standard field mask for the set of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Reservation"}, "response": {"$ref": "Reservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets an access control policy for a resource. Replaces any existing policy. Supported resources are: - Reservations To call this method, you must have the following Google IAM permissions: - `bigqueryreservation.reservations.setIamPolicy` to set policies on reservations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}:setIamPolicy", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.reservations.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Gets your permissions on a resource. Returns an empty set of permissions if the resource doesn't exist. Supported resources are: - Reservations No Google IAM permissions are required to call this method.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}:testIamPermissions", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.reservations.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"assignments": {"methods": {"create": {"description": "Creates an assignment object which allows the given project to submit jobs of a certain type using slots from the specified reservation. Currently a resource (project, folder, organization) can only have one assignment per each (job_type, location) combination, and that reservation will be used for all jobs of the matching type. Different assignments can be created on different levels of the projects, folders or organization hierarchy. During query execution, the assignment is looked up at the project, folder and organization levels in that order. The first assignment found is applied to the query. When creating assignments, it does not matter if other assignments exist at higher levels. Example: * The organization `organizationA` contains two projects, `project1` and `project2`. * Assignments for all three entities (`organizationA`, `project1`, and `project2`) could all be created and mapped to the same or different reservations. \"None\" assignments represent an absence of the assignment. Projects assigned to None use on-demand pricing. To create a \"None\" assignment, use \"none\" as a reservation_id in the parent. Example parent: `projects/myproject/locations/US/reservations/none`. Returns `google.rpc.Code.PERMISSION_DENIED` if user does not have 'bigquery.admin' permissions on the project using the reservation and the project that owns this reservation. Returns `google.rpc.Code.INVALID_ARGUMENT` when location of the assignment does not match location of the reservation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.reservations.assignments.create", "parameterOrder": ["parent"], "parameters": {"assignmentId": {"description": "The optional assignment ID. Assignment name will be generated automatically if this field is empty. This field must only contain lower case alphanumeric characters or dashes. Max length is 64 characters.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name of the assignment E.g. `projects/myproject/locations/US/reservations/team1-prod`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/assignments", "request": {"$ref": "Assignment"}, "response": {"$ref": "Assignment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a assignment. No expansion will happen. Example: * Organization `organizationA` contains two projects, `project1` and `project2`. * Reservation `res1` exists and was created previously. * CreateAssignment was used previously to define the following associations between entities and reservations: `` and `` In this example, deletion of the `` assignment won't affect the other assignment ``. After said deletion, queries from `project1` will still use `res1` while queries from `project2` will switch to use on-demand mode.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}", "httpMethod": "DELETE", "id": "bigqueryreservation.projects.locations.reservations.assignments.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource, e.g. `projects/myproject/locations/US/reservations/team1-prod/assignments/123`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. May return: * A`NOT_FOUND` error if the resource doesn't exist or you don't have the permission to view it. * An empty policy if the resource exists but doesn't have a set policy. Supported resources are: - Reservations - ReservationAssignments To call this method, you must have the following Google IAM permissions: - `bigqueryreservation.reservations.getIamPolicy` to get policies on reservations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}:getIamPolicy", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.reservations.assignments.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists assignments. Only explicitly created assignments will be returned. Example: * Organization `organizationA` contains two projects, `project1` and `project2`. * Reservation `res1` exists and was created previously. * CreateAssignment was used previously to define the following associations between entities and reservations: `` and `` In this example, ListAssignments will just return the above two assignments for reservation `res1`, and no expansion/merge will happen. The wildcard \"-\" can be used for reservations in the request. In that case all assignments belongs to the specified project and location will be listed. **Note** \"-\" cannot be used for projects nor locations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments", "httpMethod": "GET", "id": "bigqueryreservation.projects.locations.reservations.assignments.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of items to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name e.g.: `projects/myproject/locations/US/reservations/team1-prod` Or: `projects/myproject/locations/US/reservations/-`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/assignments", "response": {"$ref": "ListAssignmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "move": {"description": "Moves an assignment under a new reservation. This differs from removing an existing assignment and recreating a new one by providing a transactional change that ensures an assignee always has an associated reservation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}:move", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.reservations.assignments.move", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the assignment, e.g. `projects/myproject/locations/US/reservations/team1-prod/assignments/123`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:move", "request": {"$ref": "MoveAssignmentRequest"}, "response": {"$ref": "Assignment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing assignment. Only the `priority` field can be updated.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}", "httpMethod": "PATCH", "id": "bigqueryreservation.projects.locations.reservations.assignments.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Name of the resource. E.g.: `projects/myproject/locations/US/reservations/team1-prod/assignments/123`. The assignment_id must only contain lower case alphanumeric characters or dashes and the max length is 64 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Standard field mask for the set of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Assignment"}, "response": {"$ref": "Assignment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets an access control policy for a resource. Replaces any existing policy. Supported resources are: - Reservations To call this method, you must have the following Google IAM permissions: - `bigqueryreservation.reservations.setIamPolicy` to set policies on reservations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}:setIamPolicy", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.reservations.assignments.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Gets your permissions on a resource. Returns an empty set of permissions if the resource doesn't exist. Supported resources are: - Reservations No Google IAM permissions are required to call this method.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}:testIamPermissions", "httpMethod": "POST", "id": "bigqueryreservation.projects.locations.reservations.assignments.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250518", "rootUrl": "https://bigqueryreservation.googleapis.com/", "schemas": {"Assignment": {"description": "An assignment allows a project to submit jobs of a certain type using slots from the specified reservation.", "id": "Assignment", "properties": {"assignee": {"description": "Optional. The resource which will use the reservation. E.g. `projects/myproject`, `folders/123`, or `organizations/456`.", "type": "string"}, "enableGeminiInBigquery": {"description": "Optional. This field controls if \"Gemini in BigQuery\" (https://cloud.google.com/gemini/docs/bigquery/overview) features should be enabled for this reservation assignment, which is not on by default. \"Gemini in BigQuery\" has a distinct compliance posture from BigQuery. If this field is set to true, the assignment job type is QUERY, and the parent reservation edition is ENTERPRISE_PLUS, then the assignment will give the grantee project/organization access to \"Gemini in BigQuery\" features.", "type": "boolean"}, "jobType": {"description": "Optional. Which type of jobs will use the reservation.", "enum": ["JOB_TYPE_UNSPECIFIED", "PIPELINE", "QUERY", "ML_EXTERNAL", "BACKGROUND", "CONTINUOUS"], "enumDescriptions": ["Invalid type. Requests with this value will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "Pipeline (load/export) jobs from the project will use the reservation.", "Query jobs from the project will use the reservation.", "BigQuery ML jobs that use services external to BigQuery for model training. These jobs will not utilize idle slots from other reservations.", "Background jobs that Big<PERSON><PERSON><PERSON> runs for the customers in the background.", "Continuous SQL jobs will use this reservation. Reservations with continuous assignments cannot be mixed with non-continuous assignments."], "type": "string"}, "name": {"description": "Output only. Name of the resource. E.g.: `projects/myproject/locations/US/reservations/team1-prod/assignments/123`. The assignment_id must only contain lower case alphanumeric characters or dashes and the max length is 64 characters.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. State of the assignment.", "enum": ["STATE_UNSPECIFIED", "PENDING", "ACTIVE"], "enumDescriptions": ["Invalid state value.", "Queries from assignee will be executed as on-demand, if related assignment is pending.", "Assignment is ready."], "readOnly": true, "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Autoscale": {"description": "Auto scaling settings.", "id": "Autoscale", "properties": {"currentSlots": {"description": "Output only. The slot capacity added to this reservation when autoscale happens. Will be between [0, max_slots]. Note: after users reduce max_slots, it may take a while before it can be propagated, so current_slots may stay in the original value and could be larger than max_slots for that brief period (less than one minute)", "format": "int64", "readOnly": true, "type": "string"}, "maxSlots": {"description": "Optional. Number of slots to be scaled when needed.", "format": "int64", "type": "string"}}, "type": "object"}, "BiReservation": {"description": "Represents a BI Reservation.", "id": "BiReservation", "properties": {"name": {"description": "Identifier. The resource name of the singleton BI reservation. Reservation names have the form `projects/{project_id}/locations/{location_id}/biReservation`.", "type": "string"}, "preferredTables": {"description": "Optional. Preferred tables to use BI capacity for.", "items": {"$ref": "TableReference"}, "type": "array"}, "size": {"description": "Optional. Size of a reservation, in bytes.", "format": "int64", "type": "string"}, "updateTime": {"description": "Output only. The last update timestamp of a reservation.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CapacityCommitment": {"description": "Capacity commitment is a way to purchase compute capacity for BigQuery jobs (in the form of slots) with some committed period of usage. Annual commitments renew by default. Commitments can be removed after their commitment end time passes. In order to remove annual commitment, its plan needs to be changed to monthly or flex first. A capacity commitment resource exists as a child resource of the admin project.", "id": "CapacityCommitment", "properties": {"commitmentEndTime": {"description": "Output only. The end of the current commitment period. It is applicable only for ACTIVE capacity commitments. Note after renewal, commitment_end_time is the time the renewed commitment expires. So itwould be at a time after commitment_start_time + committed period, because we don't change commitment_start_time ,", "format": "google-datetime", "readOnly": true, "type": "string"}, "commitmentStartTime": {"description": "Output only. The start of the current commitment period. It is applicable only for ACTIVE capacity commitments. Note after the commitment is renewed, commitment_start_time won't be changed. It refers to the start time of the original commitment.", "format": "google-datetime", "readOnly": true, "type": "string"}, "edition": {"description": "Optional. Edition of the capacity commitment.", "enum": ["EDITION_UNSPECIFIED", "STANDARD", "ENTERPRISE", "ENTERPRISE_PLUS"], "enumDescriptions": ["Default value, which will be treated as ENTERPRISE.", "Standard edition.", "Enterprise edition.", "Enterprise Plus edition."], "type": "string"}, "failureStatus": {"$ref": "Status", "description": "Output only. For FAILED commitment plan, provides the reason of failure.", "readOnly": true}, "isFlatRate": {"description": "Output only. If true, the commitment is a flat-rate commitment, otherwise, it's an edition commitment.", "readOnly": true, "type": "boolean"}, "multiRegionAuxiliary": {"deprecated": true, "description": "Applicable only for commitments located within one of the BigQuery multi-regions (US or EU). If set to true, this commitment is placed in the organization's secondary region which is designated for disaster recovery purposes. If false, this commitment is placed in the organization's default region. NOTE: this is a preview feature. Project must be allow-listed in order to set this field.", "type": "boolean"}, "name": {"description": "Output only. The resource name of the capacity commitment, e.g., `projects/myproject/locations/US/capacityCommitments/123` The commitment_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "readOnly": true, "type": "string"}, "plan": {"description": "Optional. Capacity commitment commitment plan.", "enum": ["COMMITMENT_PLAN_UNSPECIFIED", "FLEX", "FLEX_FLAT_RATE", "TRIAL", "MONTHLY", "MONTHLY_FLAT_RATE", "ANNUAL", "ANNUAL_FLAT_RATE", "THREE_YEAR", "NONE"], "enumDeprecated": [false, false, true, true, false, true, false, true, false, false], "enumDescriptions": ["Invalid plan value. Requests with this value will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "Flex commitments have committed period of 1 minute after becoming ACTIVE. After that, they are not in a committed period anymore and can be removed any time.", "Same as FLEX, should only be used if flat-rate commitments are still available.", "Trial commitments have a committed period of 182 days after becoming ACTIVE. After that, they are converted to a new commitment based on the `renewal_plan`. Default `renewal_plan` for Trial commitment is Flex so that it can be deleted right after committed period ends.", "Monthly commitments have a committed period of 30 days after becoming ACTIVE. After that, they are not in a committed period anymore and can be removed any time.", "Same as MONTHLY, should only be used if flat-rate commitments are still available.", "Annual commitments have a committed period of 365 days after becoming ACTIVE. After that they are converted to a new commitment based on the renewal_plan.", "Same as ANNUAL, should only be used if flat-rate commitments are still available.", "3-year commitments have a committed period of 1095(3 * 365) days after becoming ACTIVE. After that they are converted to a new commitment based on the renewal_plan.", "Should only be used for `renewal_plan` and is only meaningful if edition is specified to values other than EDITION_UNSPECIFIED. Otherwise CreateCapacityCommitmentRequest or UpdateCapacityCommitmentRequest will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. If the renewal_plan is NONE, capacity commitment will be removed at the end of its commitment period."], "type": "string"}, "renewalPlan": {"description": "Optional. The plan this capacity commitment is converted to after commitment_end_time passes. Once the plan is changed, committed period is extended according to commitment plan. Only applicable for ANNUAL and TRIAL commitments.", "enum": ["COMMITMENT_PLAN_UNSPECIFIED", "FLEX", "FLEX_FLAT_RATE", "TRIAL", "MONTHLY", "MONTHLY_FLAT_RATE", "ANNUAL", "ANNUAL_FLAT_RATE", "THREE_YEAR", "NONE"], "enumDeprecated": [false, false, true, true, false, true, false, true, false, false], "enumDescriptions": ["Invalid plan value. Requests with this value will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "Flex commitments have committed period of 1 minute after becoming ACTIVE. After that, they are not in a committed period anymore and can be removed any time.", "Same as FLEX, should only be used if flat-rate commitments are still available.", "Trial commitments have a committed period of 182 days after becoming ACTIVE. After that, they are converted to a new commitment based on the `renewal_plan`. Default `renewal_plan` for Trial commitment is Flex so that it can be deleted right after committed period ends.", "Monthly commitments have a committed period of 30 days after becoming ACTIVE. After that, they are not in a committed period anymore and can be removed any time.", "Same as MONTHLY, should only be used if flat-rate commitments are still available.", "Annual commitments have a committed period of 365 days after becoming ACTIVE. After that they are converted to a new commitment based on the renewal_plan.", "Same as ANNUAL, should only be used if flat-rate commitments are still available.", "3-year commitments have a committed period of 1095(3 * 365) days after becoming ACTIVE. After that they are converted to a new commitment based on the renewal_plan.", "Should only be used for `renewal_plan` and is only meaningful if edition is specified to values other than EDITION_UNSPECIFIED. Otherwise CreateCapacityCommitmentRequest or UpdateCapacityCommitmentRequest will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. If the renewal_plan is NONE, capacity commitment will be removed at the end of its commitment period."], "type": "string"}, "slotCount": {"description": "Optional. Number of slots in this commitment.", "format": "int64", "type": "string"}, "state": {"description": "Output only. State of the commitment.", "enum": ["STATE_UNSPECIFIED", "PENDING", "ACTIVE", "FAILED"], "enumDescriptions": ["Invalid state value.", "Capacity commitment is pending provisioning. Pending capacity commitment does not contribute to the project's slot_capacity.", "Once slots are provisioned, capacity commitment becomes active. slot_count is added to the project's slot_capacity.", "Capacity commitment is failed to be activated by the backend."], "readOnly": true, "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "FailoverReservationRequest": {"description": "The request for ReservationService.FailoverReservation.", "id": "FailoverReservationRequest", "properties": {"failoverMode": {"description": "Optional. failover mode for the failover operation.", "enum": ["FAILOVER_MODE_UNSPECIFIED", "SOFT", "HARD"], "enumDescriptions": ["Invalid value.", "When customers initiate a soft failover, BigQuery will wait until all committed writes are replicated to the secondary.", "When customers initiate a hard failover, BigQuery will not wait until all committed writes are replicated to the secondary. There can be data loss for hard failover."], "type": "string"}}, "type": "object"}, "ListAssignmentsResponse": {"description": "The response for ReservationService.ListAssignments.", "id": "ListAssignmentsResponse", "properties": {"assignments": {"description": "List of assignments visible to the user.", "items": {"$ref": "Assignment"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "ListCapacityCommitmentsResponse": {"description": "The response for ReservationService.ListCapacityCommitments.", "id": "ListCapacityCommitmentsResponse", "properties": {"capacityCommitments": {"description": "List of capacity commitments visible to the user.", "items": {"$ref": "CapacityCommitment"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "ListReservationsResponse": {"description": "The response for ReservationService.ListReservations.", "id": "ListReservationsResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "reservations": {"description": "List of reservations visible to the user.", "items": {"$ref": "Reservation"}, "type": "array"}}, "type": "object"}, "MergeCapacityCommitmentsRequest": {"description": "The request for ReservationService.MergeCapacityCommitments.", "id": "MergeCapacityCommitmentsRequest", "properties": {"capacityCommitmentId": {"description": "Optional. The optional resulting capacity commitment ID. Capacity commitment name will be generated automatically if this field is empty. This field must only contain lower case alphanumeric characters or dashes. The first and last character cannot be a dash. Max length is 64 characters.", "type": "string"}, "capacityCommitmentIds": {"description": "Ids of capacity commitments to merge. These capacity commitments must exist under admin project and location specified in the parent. ID is the last portion of capacity commitment name e.g., 'abc' for projects/myproject/locations/US/capacityCommitments/abc", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "MoveAssignmentRequest": {"description": "The request for ReservationService.MoveAssignment. **Note**: \"bigquery.reservationAssignments.create\" permission is required on the destination_id. **Note**: \"bigquery.reservationAssignments.create\" and \"bigquery.reservationAssignments.delete\" permission are required on the related assignee.", "id": "MoveAssignmentRequest", "properties": {"assignmentId": {"description": "The optional assignment ID. A new assignment name is generated if this field is empty. This field can contain only lowercase alphanumeric characters or dashes. Max length is 64 characters.", "type": "string"}, "destinationId": {"description": "The new reservation ID, e.g.: `projects/myotherproject/locations/US/reservations/team2-prod`", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "ReplicationStatus": {"description": "Disaster Recovery(DR) replication status of the reservation.", "id": "ReplicationStatus", "properties": {"error": {"$ref": "Status", "description": "Output only. The last error encountered while trying to replicate changes from the primary to the secondary. This field is only available if the replication has not succeeded since.", "readOnly": true}, "lastErrorTime": {"description": "Output only. The time at which the last error was encountered while trying to replicate changes from the primary to the secondary. This field is only available if the replication has not succeeded since.", "format": "google-datetime", "readOnly": true, "type": "string"}, "lastReplicationTime": {"description": "Output only. A timestamp corresponding to the last change on the primary that was successfully replicated to the secondary.", "format": "google-datetime", "readOnly": true, "type": "string"}, "softFailoverStartTime": {"description": "Output only. The time at which a soft failover for the reservation and its associated datasets was initiated. After this field is set, all subsequent changes to the reservation will be rejected unless a hard failover overrides this operation. This field will be cleared once the failover is complete.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Reservation": {"description": "A reservation is a mechanism used to guarantee slots to users.", "id": "Reservation", "properties": {"autoscale": {"$ref": "Autoscale", "description": "Optional. The configuration parameters for the auto scaling feature."}, "concurrency": {"description": "Optional. Job concurrency target which sets a soft upper bound on the number of jobs that can run concurrently in this reservation. This is a soft target due to asynchronous nature of the system and various optimizations for small queries. Default value is 0 which means that concurrency target will be automatically computed by the system. NOTE: this field is exposed as target job concurrency in the Information Schema, DDL and BigQuery CLI.", "format": "int64", "type": "string"}, "creationTime": {"description": "Output only. Creation time of the reservation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "edition": {"description": "Optional. Edition of the reservation.", "enum": ["EDITION_UNSPECIFIED", "STANDARD", "ENTERPRISE", "ENTERPRISE_PLUS"], "enumDescriptions": ["Default value, which will be treated as ENTERPRISE.", "Standard edition.", "Enterprise edition.", "Enterprise Plus edition."], "type": "string"}, "ignoreIdleSlots": {"description": "Optional. If false, any query or pipeline job using this reservation will use idle slots from other reservations within the same admin project. If true, a query or pipeline job using this reservation will execute with the slot capacity specified in the slot_capacity field at most.", "type": "boolean"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels associated with this reservation. You can use these to organize and group your reservations. You can set this property when inserting or updating a reservation.", "type": "object"}, "maxSlots": {"description": "Optional. The overall max slots for the reservation, covering slot_capacity (baseline), idle slots (if ignore_idle_slots is false) and scaled slots. If present, the reservation won't use more than the specified number of slots, even if there is demand and supply (from idle slots). NOTE: capping a reservation's idle slot usage is best effort and its usage may exceed the max_slots value. However, in terms of autoscale.current_slots (which accounts for the additional added slots), it will never exceed the max_slots - baseline. This field must be set together with the scaling_mode enum value, otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. If the max_slots and scaling_mode are set, the autoscale or autoscale.max_slots field must be unset. Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. However, the autoscale field may still be in the output. The autopscale.max_slots will always show as 0 and the autoscaler.current_slots will represent the current slots from autoscaler excluding idle slots. For example, if the max_slots is 1000 and scaling_mode is AUTOSCALE_ONLY, then in the output, the autoscaler.max_slots will be 0 and the autoscaler.current_slots may be any value between 0 and 1000. If the max_slots is 1000, scaling_mode is ALL_SLOTS, the baseline is 100 and idle slots usage is 200, then in the output, the autoscaler.max_slots will be 0 and the autoscaler.current_slots will not be higher than 700. If the max_slots is 1000, scaling_mode is IDLE_SLOTS_ONLY, then in the output, the autoscaler field will be null. If the max_slots and scaling_mode are set, then the ignore_idle_slots field must be aligned with the scaling_mode enum value.(See details in ScalingMode comments). Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. Please note, the max_slots is for user to manage the part of slots greater than the baseline. Therefore, we don't allow users to set max_slots smaller or equal to the baseline as it will not be meaningful. If the field is present and slot_capacity>=max_slots, requests will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. Please note that if max_slots is set to 0, we will treat it as unset. Customers can set max_slots to 0 and set scaling_mode to SCALING_MODE_UNSPECIFIED to disable the max_slots feature.", "format": "int64", "type": "string"}, "multiRegionAuxiliary": {"deprecated": true, "description": "Applicable only for reservations located within one of the BigQuery multi-regions (US or EU). If set to true, this reservation is placed in the organization's secondary region which is designated for disaster recovery purposes. If false, this reservation is placed in the organization's default region. NOTE: this is a preview feature. Project must be allow-listed in order to set this field.", "type": "boolean"}, "name": {"description": "Identifier. The resource name of the reservation, e.g., `projects/*/locations/*/reservations/team1-prod`. The reservation_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "type": "string"}, "originalPrimaryLocation": {"description": "Output only. The location where the reservation was originally created. This is set only during the failover reservation's creation. All billing charges for the failover reservation will be applied to this location.", "readOnly": true, "type": "string"}, "primaryLocation": {"description": "Output only. The current location of the reservation's primary replica. This field is only set for reservations using the managed disaster recovery feature.", "readOnly": true, "type": "string"}, "replicationStatus": {"$ref": "ReplicationStatus", "description": "Output only. The Disaster Recovery(DR) replication status of the reservation. This is only available for the primary replicas of DR/failover reservations and provides information about the both the staleness of the secondary and the last error encountered while trying to replicate changes from the primary to the secondary. If this field is blank, it means that the reservation is either not a DR reservation or the reservation is a DR secondary or that any replication operations on the reservation have succeeded.", "readOnly": true}, "scalingMode": {"description": "Optional. The scaling mode for the reservation. If the field is present but max_slots is not present, requests will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "enum": ["SCALING_MODE_UNSPECIFIED", "AUTOSCALE_ONLY", "IDLE_SLOTS_ONLY", "ALL_SLOTS"], "enumDescriptions": ["Default value of ScalingMode.", "The reservation will scale up only using slots from autoscaling. It will not use any idle slots even if there may be some available. The upper limit that autoscaling can scale up to will be max_slots - baseline. For example, if max_slots is 1000, baseline is 200 and customer sets ScalingMode to AUTOSCALE_ONLY, then autoscalerg will scale up to 800 slots and no idle slots will be used. Please note, in this mode, the ignore_idle_slots field must be set to true. Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "The reservation will scale up using only idle slots contributed by other reservations or from unassigned commitments. If no idle slots are available it will not scale up further. If the idle slots which it is using are reclaimed by the contributing reservation(s) it may be forced to scale down. The max idle slots the reservation can be max_slots - baseline capacity. For example, if max_slots is 1000, baseline is 200 and customer sets ScalingMode to IDLE_SLOTS_ONLY, 1. if there are 1000 idle slots available in other reservations, the reservation will scale up to 1000 slots with 200 baseline and 800 idle slots. 2. if there are 500 idle slots available in other reservations, the reservation will scale up to 700 slots with 200 baseline and 300 idle slots. Please note, in this mode, the reservation might not be able to scale up to max_slots. Please note, in this mode, the ignore_idle_slots field must be set to false. Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "The reservation will scale up using all slots available to it. It will use idle slots contributed by other reservations or from unassigned commitments first. If no idle slots are available it will scale up using autoscaling. For example, if max_slots is 1000, baseline is 200 and customer sets ScalingMode to ALL_SLOTS, 1. if there are 800 idle slots available in other reservations, the reservation will scale up to 1000 slots with 200 baseline and 800 idle slots. 2. if there are 500 idle slots available in other reservations, the reservation will scale up to 1000 slots with 200 baseline, 500 idle slots and 300 autoscaling slots. 3. if there are no idle slots available in other reservations, it will scale up to 1000 slots with 200 baseline and 800 autoscaling slots. Please note, in this mode, the ignore_idle_slots field must be set to false. Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`."], "type": "string"}, "secondaryLocation": {"description": "Optional. The current location of the reservation's secondary replica. This field is only set for reservations using the managed disaster recovery feature. Users can set this in create reservation calls to create a failover reservation or in update reservation calls to convert a non-failover reservation to a failover reservation(or vice versa).", "type": "string"}, "slotCapacity": {"description": "Optional. Baseline slots available to this reservation. A slot is a unit of computational power in BigQuery, and serves as the unit of parallelism. Queries using this reservation might use more slots during runtime if ignore_idle_slots is set to false, or autoscaling is enabled. The total slot_capacity of the reservation and its siblings may exceed the total slot_count of capacity commitments. In that case, the exceeding slots will be charged with the autoscale SKU. You can increase the number of baseline slots in a reservation every few minutes. If you want to decrease your baseline slots, you are limited to once an hour if you have recently changed your baseline slot capacity and your baseline slots exceed your committed slots. Otherwise, you can decrease your baseline slots every few minutes.", "format": "int64", "type": "string"}, "updateTime": {"description": "Output only. Last update time of the reservation.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SearchAllAssignmentsResponse": {"description": "The response for ReservationService.SearchAllAssignments.", "id": "SearchAllAssignmentsResponse", "properties": {"assignments": {"description": "List of assignments visible to the user.", "items": {"$ref": "Assignment"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "SearchAssignmentsResponse": {"description": "The response for ReservationService.SearchAssignments.", "id": "SearchAssignmentsResponse", "properties": {"assignments": {"description": "List of assignments visible to the user.", "items": {"$ref": "Assignment"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SplitCapacityCommitmentRequest": {"description": "The request for ReservationService.SplitCapacityCommitment.", "id": "SplitCapacityCommitmentRequest", "properties": {"slotCount": {"description": "Number of slots in the capacity commitment after the split.", "format": "int64", "type": "string"}}, "type": "object"}, "SplitCapacityCommitmentResponse": {"description": "The response for ReservationService.SplitCapacityCommitment.", "id": "SplitCapacityCommitmentResponse", "properties": {"first": {"$ref": "CapacityCommitment", "description": "First capacity commitment, result of a split."}, "second": {"$ref": "CapacityCommitment", "description": "Second capacity commitment, result of a split."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TableReference": {"description": "Fully qualified reference to BigQuery table. Internally stored as google.cloud.bi.v1.BqTableReference.", "id": "TableReference", "properties": {"datasetId": {"description": "Optional. The ID of the dataset in the above project.", "type": "string"}, "projectId": {"description": "Optional. The assigned project ID of the project.", "type": "string"}, "tableId": {"description": "Optional. The ID of the table in the above dataset.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "BigQuery Reservation API", "version": "v1", "version_module": true}