{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://cloudbuild.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Build", "description": "Creates and manages builds on Google Cloud Platform.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/cloud-build/docs/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "cloudbuild:v1alpha1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://cloudbuild.mtls.googleapis.com/", "name": "cloudbuild", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "flatPath": "v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "cloudbuild.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "cloudbuild.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "workerPools": {"methods": {"create": {"description": "Creates a `WorkerPool` to run the builds, and returns the new worker pool.", "flatPath": "v1alpha1/projects/{projectsId}/workerPools", "httpMethod": "POST", "id": "cloudbuild.projects.workerPools.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "ID of the parent project.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha1/{+parent}/workerPools", "request": {"$ref": "WorkerPool"}, "response": {"$ref": "WorkerPool"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `WorkerPool` by its project ID and WorkerPool name.", "flatPath": "v1alpha1/projects/{projectsId}/workerPools/{workerPoolsId}", "httpMethod": "DELETE", "id": "cloudbuild.projects.workerPools.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The field will contain name of the resource requested, for example: \"projects/project-1/workerPools/workerpool-name\"", "location": "path", "pattern": "^projects/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns information about a `WorkerPool`.", "flatPath": "v1alpha1/projects/{projectsId}/workerPools/{workerPoolsId}", "httpMethod": "GET", "id": "cloudbuild.projects.workerPools.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The field will contain name of the resource requested, for example: \"projects/project-1/workerPools/workerpool-name\"", "location": "path", "pattern": "^projects/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha1/{+name}", "response": {"$ref": "WorkerPool"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List project's `WorkerPool`s.", "flatPath": "v1alpha1/projects/{projectsId}/workerPools", "httpMethod": "GET", "id": "cloudbuild.projects.workerPools.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "ID of the parent project.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha1/{+parent}/workerPools", "response": {"$ref": "ListWorkerPoolsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a `WorkerPool`.", "flatPath": "v1alpha1/projects/{projectsId}/workerPools/{workerPoolsId}", "httpMethod": "PATCH", "id": "cloudbuild.projects.workerPools.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The field will contain name of the resource requested, for example: \"projects/project-1/workerPools/workerpool-name\"", "location": "path", "pattern": "^projects/[^/]+/workerPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha1/{+name}", "request": {"$ref": "WorkerPool"}, "response": {"$ref": "WorkerPool"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}, "revision": "20230102", "rootUrl": "https://cloudbuild.googleapis.com/", "schemas": {"ApprovalConfig": {"description": "ApprovalConfig describes configuration for manual approval of a build.", "id": "ApprovalConfig", "properties": {"approvalRequired": {"description": "Whether or not approval is needed. If this is set on a build, it will become pending when created, and will need to be explicitly approved to start.", "type": "boolean"}}, "type": "object"}, "ApprovalResult": {"description": "ApprovalResult describes the decision and associated metadata of a manual approval of a build.", "id": "ApprovalResult", "properties": {"approvalTime": {"description": "Output only. The time when the approval decision was made.", "format": "google-datetime", "readOnly": true, "type": "string"}, "approverAccount": {"description": "Output only. Email of the user that called the ApproveBuild API to approve or reject a build at the time that the API was called.", "readOnly": true, "type": "string"}, "comment": {"description": "Optional. An optional comment for this manual approval result.", "type": "string"}, "decision": {"description": "Required. The decision of this manual approval.", "enum": ["DECISION_UNSPECIFIED", "APPROVED", "REJECTED"], "enumDescriptions": ["Default enum type. This should not be used.", "Build is approved.", "Build is rejected."], "type": "string"}, "url": {"description": "Optional. An optional URL tied to this manual approval result. This field is essentially the same as comment, except that it will be rendered by the UI differently. An example use case is a link to an external job that approved this Build.", "type": "string"}}, "type": "object"}, "ArtifactObjects": {"description": "Files in the workspace to upload to Cloud Storage upon successful completion of all build steps.", "id": "ArtifactObjects", "properties": {"location": {"description": "Cloud Storage bucket and optional object path, in the form \"gs://bucket/path/to/somewhere/\". (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)). Files in the workspace matching any path pattern will be uploaded to Cloud Storage with this location as a prefix.", "type": "string"}, "paths": {"description": "Path globs used to match files in the build's workspace.", "items": {"type": "string"}, "type": "array"}, "timing": {"$ref": "TimeSpan", "description": "Output only. Stores timing information for pushing all artifact objects.", "readOnly": true}}, "type": "object"}, "ArtifactResult": {"description": "An artifact that was uploaded during a build. This is a single record in the artifact manifest JSON file.", "id": "ArtifactResult", "properties": {"fileHash": {"description": "The file hash of the artifact.", "items": {"$ref": "FileHashes"}, "type": "array"}, "location": {"description": "The path of an artifact in a Google Cloud Storage bucket, with the generation number. For example, `gs://mybucket/path/to/output.jar#generation`.", "type": "string"}}, "type": "object"}, "Artifacts": {"description": "Artifacts produced by a build that should be uploaded upon successful completion of all build steps.", "id": "Artifacts", "properties": {"images": {"description": "A list of images to be pushed upon the successful completion of all build steps. The images will be pushed using the builder service account's credentials. The digests of the pushed images will be stored in the Build resource's results field. If any of the images fail to be pushed, the build is marked FAILURE.", "items": {"type": "string"}, "type": "array"}, "mavenArtifacts": {"description": "A list of Maven artifacts to be uploaded to Artifact Registry upon successful completion of all build steps. Artifacts in the workspace matching specified paths globs will be uploaded to the specified Artifact Registry repository using the builder service account's credentials. If any artifacts fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "MavenArtifact"}, "type": "array"}, "objects": {"$ref": "ArtifactObjects", "description": "A list of objects to be uploaded to Cloud Storage upon successful completion of all build steps. Files in the workspace matching specified paths globs will be uploaded to the specified Cloud Storage location using the builder service account's credentials. The location and generation of the uploaded objects will be stored in the Build resource's results field. If any objects fail to be pushed, the build is marked FAILURE."}, "pythonPackages": {"description": "A list of Python packages to be uploaded to Artifact Registry upon successful completion of all build steps. The build service account credentials will be used to perform the upload. If any objects fail to be pushed, the build is marked FAILURE.", "items": {"$ref": "PythonPackage"}, "type": "array"}}, "type": "object"}, "BatchCreateBitbucketServerConnectedRepositoriesResponse": {"description": "Response of BatchCreateBitbucketServerConnectedRepositories RPC method including all successfully connected Bitbucket Server repositories.", "id": "BatchCreateBitbucketServerConnectedRepositoriesResponse", "properties": {"bitbucketServerConnectedRepositories": {"description": "The connected Bitbucket Server repositories.", "items": {"$ref": "BitbucketServerConnectedRepository"}, "type": "array"}}, "type": "object"}, "BatchCreateBitbucketServerConnectedRepositoriesResponseMetadata": {"description": "Metadata for `BatchCreateBitbucketServerConnectedRepositories` operation.", "id": "BatchCreateBitbucketServerConnectedRepositoriesResponseMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "config": {"description": "The name of the `BitbucketServerConfig` that added connected repositories. Format: `projects/{project}/locations/{location}/bitbucketServerConfigs/{config}`", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "BatchCreateGitLabConnectedRepositoriesResponse": {"description": "Response of BatchCreateGitLabConnectedRepositories RPC method.", "id": "BatchCreateGitLabConnectedRepositoriesResponse", "properties": {"gitlabConnectedRepositories": {"description": "The GitLab connected repository requests' responses.", "items": {"$ref": "GitLabConnectedRepository"}, "type": "array"}}, "type": "object"}, "BatchCreateGitLabConnectedRepositoriesResponseMetadata": {"description": "Metadata for `BatchCreateGitLabConnectedRepositories` operation.", "id": "BatchCreateGitLabConnectedRepositoriesResponseMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "config": {"description": "The name of the `GitLabConfig` that added connected repositories. Format: `projects/{project}/locations/{location}/gitLabConfigs/{config}`", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "BatchCreateRepositoriesResponse": {"description": "Message for response of creating repositories in batch.", "id": "BatchCreateRepositoriesResponse", "properties": {"repositories": {"description": "Repository resources created.", "items": {"$ref": "Repository"}, "type": "array"}}, "type": "object"}, "BitbucketServerConnectedRepository": {"description": "/ BitbucketServerConnectedRepository represents a connected Bitbucket Server / repository.", "id": "BitbucketServerConnectedRepository", "properties": {"parent": {"description": "The name of the `BitbucketServerConfig` that added connected repository. Format: `projects/{project}/locations/{location}/bitbucketServerConfigs/{config}`", "type": "string"}, "repo": {"$ref": "BitbucketServerRepositoryId", "description": "The Bitbucket Server repositories to connect."}, "status": {"$ref": "Status", "description": "Output only. The status of the repo connection request.", "readOnly": true}}, "type": "object"}, "BitbucketServerRepositoryId": {"description": "BitbucketServerRepositoryId identifies a specific repository hosted on a Bitbucket Server.", "id": "BitbucketServerRepositoryId", "properties": {"projectKey": {"description": "Required. Identifier for the project storing the repository.", "type": "string"}, "repoSlug": {"description": "Required. Identifier for the repository.", "type": "string"}, "webhookId": {"description": "Output only. The ID of the webhook that was created for receiving events from this repo. We only create and manage a single webhook for each repo.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "Build": {"description": "A build resource in the Cloud Build API. At a high level, a `Build` describes where to find source code, how to build it (for example, the builder image to run on the source), and where to store the built artifacts. Fields can include the following variables, which will be expanded when the build is created: - $PROJECT_ID: the project ID of the build. - $PROJECT_NUMBER: the project number of the build. - $LOCATION: the location/region of the build. - $BUILD_ID: the autogenerated ID of the build. - $REPO_NAME: the source repository name specified by RepoSource. - $BRANCH_NAME: the branch name specified by RepoSource. - $TAG_NAME: the tag name specified by RepoSource. - $REVISION_ID or $COMMIT_SHA: the commit SHA specified by RepoSource or resolved from the specified branch or tag. - $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.", "id": "Build", "properties": {"approval": {"$ref": "BuildApproval", "description": "Output only. Describes this build's approval configuration, status, and result.", "readOnly": true}, "artifacts": {"$ref": "Artifacts", "description": "Artifacts produced by the build that should be uploaded upon successful completion of all build steps."}, "availableSecrets": {"$ref": "Secrets", "description": "Secrets and secret environment variables."}, "buildTriggerId": {"description": "Output only. The ID of the `BuildTrigger` that triggered this build, if it was triggered automatically.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time at which the request to create the build was received.", "format": "google-datetime", "readOnly": true, "type": "string"}, "failureInfo": {"$ref": "FailureInfo", "description": "Output only. Contains information about the build when status=FAILURE.", "readOnly": true}, "finishTime": {"description": "Output only. Time at which execution of the build was finished. The difference between finish_time and start_time is the duration of the build's execution.", "format": "google-datetime", "readOnly": true, "type": "string"}, "id": {"description": "Output only. Unique identifier of the build.", "readOnly": true, "type": "string"}, "images": {"description": "A list of images to be pushed upon the successful completion of all build steps. The images are pushed using the builder service account's credentials. The digests of the pushed images will be stored in the `Build` resource's results field. If any of the images fail to be pushed, the build status is marked `FAILURE`.", "items": {"type": "string"}, "type": "array"}, "logUrl": {"description": "Output only. URL to logs for this build in Google Cloud Console.", "readOnly": true, "type": "string"}, "logsBucket": {"description": "Google Cloud Storage bucket where logs should be written (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)). Logs file names will be of the format `${logs_bucket}/log-${build_id}.txt`.", "type": "string"}, "name": {"description": "Output only. The 'Build' name with format: `projects/{project}/locations/{location}/builds/{build}`, where {build} is a unique identifier generated by the service.", "readOnly": true, "type": "string"}, "options": {"$ref": "BuildOptions", "description": "Special options for this build."}, "projectId": {"description": "Output only. ID of the project.", "readOnly": true, "type": "string"}, "queueTtl": {"description": "TTL in queue for this build. If provided and the build is enqueued longer than this value, the build will expire and the build status will be `EXPIRED`. The TTL starts ticking from create_time.", "format": "google-duration", "type": "string"}, "results": {"$ref": "Results", "description": "Output only. Results of the build.", "readOnly": true}, "secrets": {"description": "Secrets to decrypt using Cloud Key Management Service. Note: Secret Manager is the recommended technique for managing sensitive data with Cloud Build. Use `available_secrets` to configure builds to access secrets from Secret Manager. For instructions, see: https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets", "items": {"$ref": "Secret"}, "type": "array"}, "serviceAccount": {"description": "IAM service account whose credentials will be used at build runtime. Must be of the format `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`. ACCOUNT can be email address or uniqueId of the service account. ", "type": "string"}, "source": {"$ref": "Source", "description": "The location of the source files to build."}, "sourceProvenance": {"$ref": "SourceProvenance", "description": "Output only. A permanent fixed identifier for source.", "readOnly": true}, "startTime": {"description": "Output only. Time at which execution of the build was started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "status": {"description": "Output only. Status of the build.", "enum": ["STATUS_UNKNOWN", "PENDING", "QUEUED", "WORKING", "SUCCESS", "FAILURE", "INTERNAL_ERROR", "TIMEOUT", "CANCELLED", "EXPIRED"], "enumDescriptions": ["Status of the build is unknown.", "Build has been created and is pending execution and queuing. It has not been queued.", "Build or step is queued; work has not yet begun.", "Build or step is being executed.", "Build or step finished successfully.", "Build or step failed to complete successfully.", "Build or step failed due to an internal cause.", "Build or step took longer than was allowed.", "Build or step was canceled by a user.", "Build was enqueued for longer than the value of `queue_ttl`."], "readOnly": true, "type": "string"}, "statusDetail": {"description": "Output only. Customer-readable message about the current status.", "readOnly": true, "type": "string"}, "steps": {"description": "Required. The operations to be performed on the workspace.", "items": {"$ref": "BuildStep"}, "type": "array"}, "substitutions": {"additionalProperties": {"type": "string"}, "description": "Substitutions data for `Build` resource.", "type": "object"}, "tags": {"description": "Tags for annotation of a `Build`. These are not docker tags.", "items": {"type": "string"}, "type": "array"}, "timeout": {"description": "Amount of time that this build should be allowed to run, to second granularity. If this amount of time elapses, work on the build will cease and the build status will be `TIMEOUT`. `timeout` starts ticking from `startTime`. Default time is ten minutes.", "format": "google-duration", "type": "string"}, "timing": {"additionalProperties": {"$ref": "TimeSpan"}, "description": "Output only. Stores timing information for phases of the build. Valid keys are: * BUILD: time to execute all build steps. * PUSH: time to push all artifacts including docker images and non docker artifacts. * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build. If the build does not specify source or images, these keys will not be included.", "readOnly": true, "type": "object"}, "warnings": {"description": "Output only. Non-fatal problems encountered during the execution of the build.", "items": {"$ref": "Warning"}, "readOnly": true, "type": "array"}}, "type": "object"}, "BuildApproval": {"description": "BuildApproval describes a build's approval configuration, state, and result.", "id": "BuildApproval", "properties": {"config": {"$ref": "ApprovalConfig", "description": "Output only. Configuration for manual approval of this build.", "readOnly": true}, "result": {"$ref": "ApprovalResult", "description": "Output only. Result of manual approval for this Build.", "readOnly": true}, "state": {"description": "Output only. The state of this build's approval.", "enum": ["STATE_UNSPECIFIED", "PENDING", "APPROVED", "REJECTED", "CANCELLED"], "enumDescriptions": ["Default enum type. This should not be used.", "Build approval is pending.", "Build approval has been approved.", "Build approval has been rejected.", "Build was cancelled while it was still pending approval."], "readOnly": true, "type": "string"}}, "type": "object"}, "BuildOperationMetadata": {"description": "Metadata for build operations.", "id": "BuildOperationMetadata", "properties": {"build": {"$ref": "Build", "description": "The build that the operation is tracking."}}, "type": "object"}, "BuildOptions": {"description": "Optional arguments to enable specific features of builds.", "id": "BuildOptions", "properties": {"diskSizeGb": {"description": "Requested disk size for the VM that runs the build. Note that this is *NOT* \"disk free\"; some of the space will be used by the operating system and build utilities. Also note that this is the minimum disk size that will be allocated for the build -- the build may run with a larger disk than requested. At present, the maximum disk size is 2000GB; builds that request more than the maximum are rejected with an error.", "format": "int64", "type": "string"}, "dynamicSubstitutions": {"description": "Option to specify whether or not to apply bash style string operations to the substitutions. NOTE: this is always enabled for triggered builds and cannot be overridden in the build configuration file.", "type": "boolean"}, "env": {"description": "A list of global environment variable definitions that will exist for all build steps in this build. If a variable is defined in both globally and in a build step, the variable will use the build step value. The elements are of the form \"KEY=VALUE\" for the environment variable \"KEY\" being given the value \"VALUE\".", "items": {"type": "string"}, "type": "array"}, "logStreamingOption": {"description": "Option to define build log streaming behavior to Google Cloud Storage.", "enum": ["STREAM_DEFAULT", "STREAM_ON", "STREAM_OFF"], "enumDescriptions": ["Service may automatically determine build log streaming behavior.", "Build logs should be streamed to Google Cloud Storage.", "Build logs should not be streamed to Google Cloud Storage; they will be written when the build is completed."], "type": "string"}, "logging": {"description": "Option to specify the logging mode, which determines if and where build logs are stored.", "enum": ["LOGGING_UNSPECIFIED", "LEGACY", "GCS_ONLY", "STACKDRIVER_ONLY", "CLOUD_LOGGING_ONLY", "NONE"], "enumDescriptions": ["The service determines the logging mode. The default is `LEGACY`. Do not rely on the default logging behavior as it may change in the future.", "Build logs are stored in Cloud Logging and Cloud Storage.", "Build logs are stored in Cloud Storage.", "This option is the same as CLOUD_LOGGING_ONLY.", "Build logs are stored in Cloud Logging. Selecting this option will not allow [logs streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).", "Turn off all logging. No build logs will be captured."], "type": "string"}, "machineType": {"description": "Compute Engine machine type on which to run the build.", "enum": ["UNSPECIFIED", "N1_HIGHCPU_8", "N1_HIGHCPU_32", "E2_HIGHCPU_8", "E2_HIGHCPU_32"], "enumDescriptions": ["Standard machine type.", "Highcpu machine with 8 CPUs.", "Highcpu machine with 32 CPUs.", "Highcpu e2 machine with 8 CPUs.", "Highcpu e2 machine with 32 CPUs."], "type": "string"}, "pool": {"$ref": "PoolOption", "description": "Optional. Specification for execution on a `WorkerPool`. See [running builds in a private pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-private-pool) for more information."}, "requestedVerifyOption": {"description": "Requested verifiability options.", "enum": ["NOT_VERIFIED", "VERIFIED"], "enumDescriptions": ["Not a verifiable build (the default).", "Build must be verified."], "type": "string"}, "secretEnv": {"description": "A list of global environment variables, which are encrypted using a Cloud Key Management Service crypto key. These values must be specified in the build's `Secret`. These variables will be available to all build steps in this build.", "items": {"type": "string"}, "type": "array"}, "sourceProvenanceHash": {"description": "Requested hash for SourceProvenance.", "items": {"enum": ["NONE", "SHA256", "MD5"], "enumDescriptions": ["No hash requested.", "Use a sha256 hash.", "Use a md5 hash."], "type": "string"}, "type": "array"}, "substitutionOption": {"description": "Option to specify behavior when there is an error in the substitution checks. NOTE: this is always set to ALLOW_LOOSE for triggered builds and cannot be overridden in the build configuration file.", "enum": ["MUST_MATCH", "ALLOW_LOOSE"], "enumDescriptions": ["Fails the build if error in substitutions checks, like missing a substitution in the template or in the map.", "Do not fail the build if error in substitutions checks."], "type": "string"}, "volumes": {"description": "Global list of volumes to mount for ALL build steps Each volume is created as an empty volume prior to starting the build process. Upon completion of the build, volumes and their contents are discarded. Global volume names and paths cannot conflict with the volumes defined a build step. Using a global volume in a build with only one step is not valid as it is indicative of a build request with an incorrect configuration.", "items": {"$ref": "Volume"}, "type": "array"}, "workerPool": {"description": "This field deprecated; please use `pool.name` instead.", "type": "string"}}, "type": "object"}, "BuildStep": {"description": "A step in the build pipeline.", "id": "BuildStep", "properties": {"allowExitCodes": {"description": "Allow this build step to fail without failing the entire build if and only if the exit code is one of the specified codes. If allow_failure is also specified, this field will take precedence.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "allowFailure": {"description": "Allow this build step to fail without failing the entire build. If false, the entire build will fail if this step fails. Otherwise, the build will succeed, but this step will still have a failure status. Error information will be reported in the failure_detail field.", "type": "boolean"}, "args": {"description": "A list of arguments that will be presented to the step when it is started. If the image used to run the step's container has an entrypoint, the `args` are used as arguments to that entrypoint. If the image does not define an entrypoint, the first element in args is used as the entrypoint, and the remainder will be used as arguments.", "items": {"type": "string"}, "type": "array"}, "dir": {"description": "Working directory to use when running this step's container. If this value is a relative path, it is relative to the build's working directory. If this value is absolute, it may be outside the build's working directory, in which case the contents of the path may not be persisted across build step executions, unless a `volume` for that path is specified. If the build specifies a `RepoSource` with `dir` and a step with a `dir`, which specifies an absolute path, the `RepoSource` `dir` is ignored for the step's execution.", "type": "string"}, "entrypoint": {"description": "Entrypoint to be used instead of the build step image's default entrypoint. If unset, the image's default entrypoint is used.", "type": "string"}, "env": {"description": "A list of environment variable definitions to be used when running a step. The elements are of the form \"KEY=VALUE\" for the environment variable \"KEY\" being given the value \"VALUE\".", "items": {"type": "string"}, "type": "array"}, "exitCode": {"description": "Output only. Return code from running the step.", "format": "int32", "readOnly": true, "type": "integer"}, "id": {"description": "Unique identifier for this build step, used in `wait_for` to reference this build step as a dependency.", "type": "string"}, "name": {"description": "Required. The name of the container image that will run this particular build step. If the image is available in the host's Docker daemon's cache, it will be run directly. If not, the host will attempt to pull the image first, using the builder service account's credentials if necessary. The Docker daemon's cache will already have the latest versions of all of the officially supported build steps ([https://github.com/GoogleCloudPlatform/cloud-builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The Docker daemon will also have cached many of the layers for some popular images, like \"ubuntu\", \"debian\", but they will be refreshed at the time you attempt to use them. If you built an image in a previous build step, it will be stored in the host's Docker daemon's cache and is available to use as the name for a later build step.", "type": "string"}, "pullTiming": {"$ref": "TimeSpan", "description": "Output only. Stores timing information for pulling this build step's builder image only.", "readOnly": true}, "script": {"description": "A shell script to be executed in the step. When script is provided, the user cannot specify the entrypoint or args.", "type": "string"}, "secretEnv": {"description": "A list of environment variables which are encrypted using a Cloud Key Management Service crypto key. These values must be specified in the build's `Secret`.", "items": {"type": "string"}, "type": "array"}, "status": {"description": "Output only. Status of the build step. At this time, build step status is only updated on build completion; step status is not updated in real-time as the build progresses.", "enum": ["STATUS_UNKNOWN", "PENDING", "QUEUED", "WORKING", "SUCCESS", "FAILURE", "INTERNAL_ERROR", "TIMEOUT", "CANCELLED", "EXPIRED"], "enumDescriptions": ["Status of the build is unknown.", "Build has been created and is pending execution and queuing. It has not been queued.", "Build or step is queued; work has not yet begun.", "Build or step is being executed.", "Build or step finished successfully.", "Build or step failed to complete successfully.", "Build or step failed due to an internal cause.", "Build or step took longer than was allowed.", "Build or step was canceled by a user.", "Build was enqueued for longer than the value of `queue_ttl`."], "readOnly": true, "type": "string"}, "timeout": {"description": "Time limit for executing this build step. If not defined, the step has no time limit and will be allowed to continue to run until either it completes or the build itself times out.", "format": "google-duration", "type": "string"}, "timing": {"$ref": "TimeSpan", "description": "Output only. Stores timing information for executing this build step.", "readOnly": true}, "volumes": {"description": "List of volumes to mount into the build step. Each volume is created as an empty volume prior to execution of the build step. Upon completion of the build, volumes and their contents are discarded. Using a named volume in only one step is not valid as it is indicative of a build request with an incorrect configuration.", "items": {"$ref": "Volume"}, "type": "array"}, "waitFor": {"description": "The ID(s) of the step(s) that this build step depends on. This build step will not start until all the build steps in `wait_for` have completed successfully. If `wait_for` is empty, this build step will start when all previous build steps in the `Build.Steps` list have completed successfully.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BuiltImage": {"description": "An image built by the pipeline.", "id": "BuiltImage", "properties": {"digest": {"description": "Docker Registry 2.0 digest.", "type": "string"}, "name": {"description": "Name used to push the container image to Google Container Registry, as presented to `docker push`.", "type": "string"}, "pushTiming": {"$ref": "TimeSpan", "description": "Output only. Stores timing information for pushing the specified image.", "readOnly": true}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CreateBitbucketServerConfigOperationMetadata": {"description": "Metadata for `CreateBitbucketServerConfig` operation.", "id": "CreateBitbucketServerConfigOperationMetadata", "properties": {"bitbucketServerConfig": {"description": "The resource name of the BitbucketServerConfig to be created. Format: `projects/{project}/locations/{location}/bitbucketServerConfigs/{id}`.", "type": "string"}, "completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "CreateGitHubEnterpriseConfigOperationMetadata": {"description": "Metadata for `CreateGithubEnterpriseConfig` operation.", "id": "CreateGitHubEnterpriseConfigOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "githubEnterpriseConfig": {"description": "The resource name of the GitHubEnterprise to be created. Format: `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.", "type": "string"}}, "type": "object"}, "CreateGitLabConfigOperationMetadata": {"description": "Metadata for `CreateGitLabConfig` operation.", "id": "CreateGitLabConfigOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "gitlabConfig": {"description": "The resource name of the GitLabConfig to be created. Format: `projects/{project}/locations/{location}/gitlabConfigs/{id}`.", "type": "string"}}, "type": "object"}, "CreateWorkerPoolOperationMetadata": {"description": "Metadata for the `CreateWorkerPool` operation.", "id": "CreateWorkerPoolOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "workerPool": {"description": "The resource name of the `WorkerPool` to create. Format: `projects/{project}/locations/{location}/workerPools/{worker_pool}`.", "type": "string"}}, "type": "object"}, "DeleteBitbucketServerConfigOperationMetadata": {"description": "Metadata for `DeleteBitbucketServerConfig` operation.", "id": "DeleteBitbucketServerConfigOperationMetadata", "properties": {"bitbucketServerConfig": {"description": "The resource name of the BitbucketServerConfig to be deleted. Format: `projects/{project}/locations/{location}/bitbucketServerConfigs/{id}`.", "type": "string"}, "completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "DeleteGitHubEnterpriseConfigOperationMetadata": {"description": "Metadata for `DeleteGitHubEnterpriseConfig` operation.", "id": "DeleteGitHubEnterpriseConfigOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "githubEnterpriseConfig": {"description": "The resource name of the GitHubEnterprise to be deleted. Format: `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.", "type": "string"}}, "type": "object"}, "DeleteGitLabConfigOperationMetadata": {"description": "Metadata for `DeleteGitLabConfig` operation.", "id": "DeleteGitLabConfigOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "gitlabConfig": {"description": "The resource name of the GitLabConfig to be created. Format: `projects/{project}/locations/{location}/gitlabConfigs/{id}`.", "type": "string"}}, "type": "object"}, "DeleteWorkerPoolOperationMetadata": {"description": "Metadata for the `DeleteWorkerPool` operation.", "id": "DeleteWorkerPoolOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "workerPool": {"description": "The resource name of the `WorkerPool` being deleted. Format: `projects/{project}/locations/{location}/workerPools/{worker_pool}`.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "FailureInfo": {"description": "A fatal problem encountered during the execution of the build.", "id": "FailureInfo", "properties": {"detail": {"description": "Explains the failure issue in more detail using hard-coded text.", "type": "string"}, "type": {"description": "The name of the failure.", "enum": ["FAILURE_TYPE_UNSPECIFIED", "PUSH_FAILED", "PUSH_IMAGE_NOT_FOUND", "PUSH_NOT_AUTHORIZED", "LOGGING_FAILURE", "USER_BUILD_STEP", "FETCH_SOURCE_FAILED"], "enumDescriptions": ["Type unspecified", "Unable to push the image to the repository.", "Final image not found.", "Unauthorized push of the final image.", "Backend logging failures. Should retry.", "A build step has failed.", "The source fetching has failed."], "type": "string"}}, "type": "object"}, "FileHashes": {"description": "Container message for hashes of byte content of files, used in SourceProvenance messages to verify integrity of source input to the build.", "id": "FileHashes", "properties": {"fileHash": {"description": "Collection of file hashes.", "items": {"$ref": "Hash"}, "type": "array"}}, "type": "object"}, "GitLabConnectedRepository": {"description": "GitLabConnectedRepository represents a GitLab connected repository request response.", "id": "GitLabConnectedRepository", "properties": {"parent": {"description": "The name of the `GitLabConfig` that added connected repository. Format: `projects/{project}/locations/{location}/gitLabConfigs/{config}`", "type": "string"}, "repo": {"$ref": "GitLabRepositoryId", "description": "The GitLab repositories to connect."}, "status": {"$ref": "Status", "description": "Output only. The status of the repo connection request.", "readOnly": true}}, "type": "object"}, "GitLabRepositoryId": {"description": "GitLabRepositoryId identifies a specific repository hosted on GitLab.com or GitLabEnterprise", "id": "GitLabRepositoryId", "properties": {"id": {"description": "Required. Identifier for the repository. example: \"namespace/project-slug\", namespace is usually the username or group ID", "type": "string"}, "webhookId": {"description": "Output only. The ID of the webhook that was created for receiving events from this repo. We only create and manage a single webhook for each repo.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleDevtoolsCloudbuildV2OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleDevtoolsCloudbuildV2OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "HTTPDelivery": {"description": "HTTPDelivery is the delivery configuration for an HTTP notification.", "id": "HTTPDelivery", "properties": {"uri": {"description": "The URI to which JSON-containing HTTP POST requests should be sent.", "type": "string"}}, "type": "object"}, "Hash": {"description": "Container message for hash values.", "id": "Hash", "properties": {"type": {"description": "The type of hash that was performed.", "enum": ["NONE", "SHA256", "MD5"], "enumDescriptions": ["No hash requested.", "Use a sha256 hash.", "Use a md5 hash."], "type": "string"}, "value": {"description": "The hash value.", "format": "byte", "type": "string"}}, "type": "object"}, "InlineSecret": {"description": "Pairs a set of secret environment variables mapped to encrypted values with the Cloud KMS key to use to decrypt the value.", "id": "InlineSecret", "properties": {"envMap": {"additionalProperties": {"format": "byte", "type": "string"}, "description": "Map of environment variable name to its encrypted value. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step. Values can be at most 64 KB in size. There can be at most 100 secret values across all of a build's secrets.", "type": "object"}, "kmsKeyName": {"description": "Resource name of Cloud KMS crypto key to decrypt the encrypted value. In format: projects/*/locations/*/keyRings/*/cryptoKeys/*", "type": "string"}}, "type": "object"}, "ListWorkerPoolsResponse": {"description": "Response containing existing `WorkerPools`.", "id": "ListWorkerPoolsResponse", "properties": {"workerPools": {"description": "`WorkerPools` for the project.", "items": {"$ref": "WorkerPool"}, "type": "array"}}, "type": "object"}, "MavenArtifact": {"description": "A Maven artifact to upload to Artifact Registry upon successful completion of all build steps.", "id": "MavenArtifact", "properties": {"artifactId": {"description": "Maven `artifactId` value used when uploading the artifact to Artifact Registry.", "type": "string"}, "groupId": {"description": "Maven `groupId` value used when uploading the artifact to Artifact Registry.", "type": "string"}, "path": {"description": "Path to an artifact in the build's workspace to be uploaded to Artifact Registry. This can be either an absolute path, e.g. /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar or a relative path from /workspace, e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.", "type": "string"}, "repository": {"description": "Artifact Registry repository, in the form \"https://$REGION-maven.pkg.dev/$PROJECT/$REPOSITORY\" Artifact in the workspace specified by path will be uploaded to Artifact Registry with this location as a prefix.", "type": "string"}, "version": {"description": "Maven `version` value used when uploading the artifact to Artifact Registry.", "type": "string"}}, "type": "object"}, "Network": {"description": "Network describes the GCP network used to create workers in.", "id": "Network", "properties": {"network": {"description": "Network on which the workers are created. \"default\" network is used if empty.", "type": "string"}, "projectId": {"description": "Project id containing the defined network and subnetwork. For a peered VPC, this will be the same as the project_id in which the workers are created. For a shared VPC, this will be the project sharing the network with the project_id project in which workers will be created. For custom workers with no VPC, this will be the same as project_id.", "type": "string"}, "subnetwork": {"description": "Subnetwork on which the workers are created. \"default\" subnetwork is used if empty.", "type": "string"}}, "type": "object"}, "Notification": {"description": "Notification is the container which holds the data that is relevant to this particular notification.", "id": "Notification", "properties": {"filter": {"description": "The filter string to use for notification filtering. Currently, this is assumed to be a CEL program. See https://opensource.google/projects/cel for more.", "type": "string"}, "httpDelivery": {"$ref": "HTTPDelivery", "description": "Configuration for HTTP delivery."}, "slackDelivery": {"$ref": "SlackDelivery", "description": "Configuration for Slack delivery."}, "smtpDelivery": {"$ref": "SMTPDelivery", "description": "Configuration for SMTP (email) delivery."}, "structDelivery": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Escape hatch for users to supply custom delivery configs.", "type": "object"}}, "type": "object"}, "NotifierConfig": {"description": "NotifierConfig is the top-level configuration message.", "id": "NotifierConfig", "properties": {"apiVersion": {"description": "The API version of this configuration format.", "type": "string"}, "kind": {"description": "The type of notifier to use (e.g. SMTPNotifier).", "type": "string"}, "metadata": {"$ref": "NotifierMetadata", "description": "Metadata for referring to/handling/deploying this notifier."}, "spec": {"$ref": "NotifierSpec", "description": "The actual configuration for this notifier."}}, "type": "object"}, "NotifierMetadata": {"description": "NotifierMetadata contains the data which can be used to reference or describe this notifier.", "id": "NotifierMetadata", "properties": {"name": {"description": "The human-readable and user-given name for the notifier. For example: \"repo-merge-email-notifier\".", "type": "string"}, "notifier": {"description": "The string representing the name and version of notifier to deploy. Expected to be of the form of \"/:\". For example: \"gcr.io/my-project/notifiers/smtp:1.2.34\".", "type": "string"}}, "type": "object"}, "NotifierSecret": {"description": "NotifierSecret is the container that maps a secret name (reference) to its Google Cloud Secret Manager resource path.", "id": "NotifierSecret", "properties": {"name": {"description": "Name is the local name of the secret, such as the verbatim string \"my-smtp-password\".", "type": "string"}, "value": {"description": "Value is interpreted to be a resource path for fetching the actual (versioned) secret data for this secret. For example, this would be a Google Cloud Secret Manager secret version resource path like: \"projects/my-project/secrets/my-secret/versions/latest\".", "type": "string"}}, "type": "object"}, "NotifierSecretRef": {"description": "NotifierSecretRef contains the reference to a secret stored in the corresponding NotifierSpec.", "id": "NotifierSecretRef", "properties": {"secretRef": {"description": "The value of `secret_ref` should be a `name` that is registered in a `Secret` in the `secrets` list of the `Spec`.", "type": "string"}}, "type": "object"}, "NotifierSpec": {"description": "NotifierSpec is the configuration container for notifications.", "id": "NotifierSpec", "properties": {"notification": {"$ref": "Notification", "description": "The configuration of this particular notifier."}, "secrets": {"description": "Configurations for secret resources used by this particular notifier.", "items": {"$ref": "NotifierSecret"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal response of the operation in case of success. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "cancelRequested": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "statusDetail": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "PoolOption": {"description": "Details about how a build should be executed on a `WorkerPool`. See [running builds in a private pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-private-pool) for more information.", "id": "PoolOption", "properties": {"name": {"description": "The `WorkerPool` resource to execute the build on. You must have `cloudbuild.workerpools.use` on the project hosting the WorkerPool. Format projects/{project}/locations/{location}/workerPools/{workerPoolId}", "type": "string"}}, "type": "object"}, "ProcessAppManifestCallbackOperationMetadata": {"description": "Metadata for `ProcessAppManifestCallback` operation.", "id": "ProcessAppManifestCallbackOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "githubEnterpriseConfig": {"description": "The resource name of the GitHubEnterprise to be created. Format: `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.", "type": "string"}}, "type": "object"}, "PythonPackage": {"description": "Python package to upload to Artifact Registry upon successful completion of all build steps. A package can encapsulate multiple objects to be uploaded to a single repository.", "id": "PythonPackage", "properties": {"paths": {"description": "Path globs used to match files in the build's workspace. For Python/ Twine, this is usually `dist/*`, and sometimes additionally an `.asc` file.", "items": {"type": "string"}, "type": "array"}, "repository": {"description": "Artifact Registry repository, in the form \"https://$REGION-python.pkg.dev/$PROJECT/$REPOSITORY\" Files in the workspace matching any path pattern will be uploaded to Artifact Registry with this location as a prefix.", "type": "string"}}, "type": "object"}, "RepoSource": {"description": "Location of the source in a Google Cloud Source Repository.", "id": "RepoSource", "properties": {"branchName": {"description": "Regex matching branches to build. The syntax of the regular expressions accepted is the syntax accepted by RE2 and described at https://github.com/google/re2/wiki/Syntax", "type": "string"}, "commitSha": {"description": "Explicit commit SHA to build.", "type": "string"}, "dir": {"description": "Directory, relative to the source root, in which to run the build. This must be a relative path. If a step's `dir` is specified and is an absolute path, this value is ignored for that step's execution.", "type": "string"}, "invertRegex": {"description": "Only trigger a build if the revision regex does NOT match the revision regex.", "type": "boolean"}, "projectId": {"description": "ID of the project that owns the Cloud Source Repository. If omitted, the project ID requesting the build is assumed.", "type": "string"}, "repoName": {"description": "Name of the Cloud Source Repository.", "type": "string"}, "substitutions": {"additionalProperties": {"type": "string"}, "description": "Substitutions to use in a triggered build. Should only be used with RunBuildTrigger", "type": "object"}, "tagName": {"description": "Regex matching tags to build. The syntax of the regular expressions accepted is the syntax accepted by RE2 and described at https://github.com/google/re2/wiki/Syntax", "type": "string"}}, "type": "object"}, "Repository": {"description": "A repository associated to a parent connection.", "id": "Repository", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Allows clients to store small amounts of arbitrary data.", "type": "object"}, "createTime": {"description": "Output only. Server assigned timestamp for when the connection was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "name": {"description": "Immutable. Resource name of the repository, in the format `projects/*/locations/*/connections/*/repositories/*`.", "type": "string"}, "remoteUri": {"description": "Required. Git Clone HTTPS URI.", "type": "string"}, "updateTime": {"description": "Output only. Server assigned timestamp for when the connection was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Results": {"description": "Artifacts created by the build pipeline.", "id": "Results", "properties": {"artifactManifest": {"description": "Path to the artifact manifest for non-container artifacts uploaded to Cloud Storage. Only populated when artifacts are uploaded to Cloud Storage.", "type": "string"}, "artifactTiming": {"$ref": "TimeSpan", "description": "Time to push all non-container artifacts to Cloud Storage."}, "buildStepImages": {"description": "List of build step digests, in the order corresponding to build step indices.", "items": {"type": "string"}, "type": "array"}, "buildStepOutputs": {"description": "List of build step outputs, produced by builder images, in the order corresponding to build step indices. [Cloud Builders](https://cloud.google.com/cloud-build/docs/cloud-builders) can produce this output by writing to `$BUILDER_OUTPUT/output`. Only the first 4KB of data is stored.", "items": {"format": "byte", "type": "string"}, "type": "array"}, "images": {"description": "Container images that were built as a part of the build.", "items": {"$ref": "BuiltImage"}, "type": "array"}, "mavenArtifacts": {"description": "Maven artifacts uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "UploadedMavenArtifact"}, "type": "array"}, "numArtifacts": {"description": "Number of non-container artifacts uploaded to Cloud Storage. Only populated when artifacts are uploaded to Cloud Storage.", "format": "int64", "type": "string"}, "pythonPackages": {"description": "Python artifacts uploaded to Artifact Registry at the end of the build.", "items": {"$ref": "UploadedPythonPackage"}, "type": "array"}}, "type": "object"}, "RunWorkflowCustomOperationMetadata": {"description": "Represents the custom metadata of the RunWorkflow long-running operation.", "id": "RunWorkflowCustomOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "pipelineRunId": {"description": "Output only. ID of the pipeline run created by RunWorkflow.", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "SMTPDelivery": {"description": "SMTPDelivery is the delivery configuration for an SMTP (email) notification.", "id": "SMTPDelivery", "properties": {"fromAddress": {"description": "This is the SMTP account/email that appears in the `From:` of the email. If empty, it is assumed to be sender.", "type": "string"}, "password": {"$ref": "NotifierSecretRef", "description": "The SMTP sender's password."}, "port": {"description": "The SMTP port of the server.", "type": "string"}, "recipientAddresses": {"description": "This is the list of addresses to which we send the email (i.e. in the `To:` of the email).", "items": {"type": "string"}, "type": "array"}, "senderAddress": {"description": "This is the SMTP account/email that is used to send the message.", "type": "string"}, "server": {"description": "The address of the SMTP server.", "type": "string"}}, "type": "object"}, "Secret": {"description": "Pairs a set of secret environment variables containing encrypted values with the Cloud KMS key to use to decrypt the value. Note: Use `kmsKeyName` with `available_secrets` instead of using `kmsKeyName` with `secret`. For instructions see: https://cloud.google.com/cloud-build/docs/securing-builds/use-encrypted-credentials.", "id": "Secret", "properties": {"kmsKeyName": {"description": "Cloud KMS key name to use to decrypt these envs.", "type": "string"}, "secretEnv": {"additionalProperties": {"format": "byte", "type": "string"}, "description": "Map of environment variable name to its encrypted value. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step. Values can be at most 64 KB in size. There can be at most 100 secret values across all of a build's secrets.", "type": "object"}}, "type": "object"}, "SecretManagerSecret": {"description": "Pairs a secret environment variable with a SecretVersion in Secret Manager.", "id": "SecretManagerSecret", "properties": {"env": {"description": "Environment variable name to associate with the secret. Secret environment variables must be unique across all of a build's secrets, and must be used by at least one build step.", "type": "string"}, "versionName": {"description": "Resource name of the SecretVersion. In format: projects/*/secrets/*/versions/*", "type": "string"}}, "type": "object"}, "Secrets": {"description": "Secrets and secret environment variables.", "id": "Secrets", "properties": {"inline": {"description": "Secrets encrypted with KMS key and the associated secret environment variable.", "items": {"$ref": "InlineSecret"}, "type": "array"}, "secretManager": {"description": "Secrets in Secret Manager and associated secret environment variable.", "items": {"$ref": "SecretManagerSecret"}, "type": "array"}}, "type": "object"}, "SlackDelivery": {"description": "SlackDelivery is the delivery configuration for delivering Slack messages via webhooks. See Slack webhook documentation at: https://api.slack.com/messaging/webhooks.", "id": "SlackDelivery", "properties": {"webhookUri": {"$ref": "NotifierSecretRef", "description": "The secret reference for the Slack webhook URI for sending messages to a channel."}}, "type": "object"}, "Source": {"description": "Location of the source in a supported storage service.", "id": "Source", "properties": {"repoSource": {"$ref": "RepoSource", "description": "If provided, get the source from this location in a Cloud Source Repository."}, "storageSource": {"$ref": "StorageSource", "description": "If provided, get the source from this location in Google Cloud Storage."}, "storageSourceManifest": {"$ref": "StorageSourceManifest", "description": "If provided, get the source from this manifest in Google Cloud Storage. This feature is in Preview; see description [here](https://github.com/GoogleCloudPlatform/cloud-builders/tree/master/gcs-fetcher)."}}, "type": "object"}, "SourceProvenance": {"description": "Provenance of the source. Ways to find the original source, or verify that some source was used for this build.", "id": "SourceProvenance", "properties": {"fileHashes": {"additionalProperties": {"$ref": "FileHashes"}, "description": "Output only. Hash(es) of the build source, which can be used to verify that the original source integrity was maintained in the build. Note that `FileHashes` will only be populated if `BuildOptions` has requested a `SourceProvenanceHash`. The keys to this map are file paths used as build source and the values contain the hash values for those files. If the build source came in a single package such as a gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path to that file.", "readOnly": true, "type": "object"}, "resolvedRepoSource": {"$ref": "RepoSource", "description": "A copy of the build's `source.repo_source`, if exists, with any revisions resolved."}, "resolvedStorageSource": {"$ref": "StorageSource", "description": "A copy of the build's `source.storage_source`, if exists, with any generations resolved."}, "resolvedStorageSourceManifest": {"$ref": "StorageSourceManifest", "description": "A copy of the build's `source.storage_source_manifest`, if exists, with any revisions resolved. This feature is in Preview."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StorageSource": {"description": "Location of the source in an archive file in Google Cloud Storage.", "id": "StorageSource", "properties": {"bucket": {"description": "Google Cloud Storage bucket containing the source (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).", "type": "string"}, "generation": {"description": "Google Cloud Storage generation for the object. If the generation is omitted, the latest generation will be used.", "format": "int64", "type": "string"}, "object": {"description": "Google Cloud Storage object containing the source. This object must be a zipped (`.zip`) or gzipped archive file (`.tar.gz`) containing source to build.", "type": "string"}}, "type": "object"}, "StorageSourceManifest": {"description": "Location of the source manifest in Google Cloud Storage. This feature is in Preview; see description [here](https://github.com/GoogleCloudPlatform/cloud-builders/tree/master/gcs-fetcher).", "id": "StorageSourceManifest", "properties": {"bucket": {"description": "Google Cloud Storage bucket containing the source manifest (see [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).", "type": "string"}, "generation": {"description": "Google Cloud Storage generation for the object. If the generation is omitted, the latest generation will be used.", "format": "int64", "type": "string"}, "object": {"description": "Google Cloud Storage object containing the source manifest. This object must be a JSON file.", "type": "string"}}, "type": "object"}, "TimeSpan": {"description": "Start and end times for a build execution phase.", "id": "TimeSpan", "properties": {"endTime": {"description": "End of time span.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Start of time span.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UpdateBitbucketServerConfigOperationMetadata": {"description": "Metadata for `UpdateBitbucketServerConfig` operation.", "id": "UpdateBitbucketServerConfigOperationMetadata", "properties": {"bitbucketServerConfig": {"description": "The resource name of the BitbucketServerConfig to be updated. Format: `projects/{project}/locations/{location}/bitbucketServerConfigs/{id}`.", "type": "string"}, "completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UpdateGitHubEnterpriseConfigOperationMetadata": {"description": "Metadata for `UpdateGitHubEnterpriseConfig` operation.", "id": "UpdateGitHubEnterpriseConfigOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "githubEnterpriseConfig": {"description": "The resource name of the GitHubEnterprise to be updated. Format: `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.", "type": "string"}}, "type": "object"}, "UpdateGitLabConfigOperationMetadata": {"description": "Metadata for `UpdateGitLabConfig` operation.", "id": "UpdateGitLabConfigOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "gitlabConfig": {"description": "The resource name of the GitLabConfig to be created. Format: `projects/{project}/locations/{location}/gitlabConfigs/{id}`.", "type": "string"}}, "type": "object"}, "UpdateWorkerPoolOperationMetadata": {"description": "Metadata for the `UpdateWorkerPool` operation.", "id": "UpdateWorkerPoolOperationMetadata", "properties": {"completeTime": {"description": "Time the operation was completed.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Time the operation was created.", "format": "google-datetime", "type": "string"}, "workerPool": {"description": "The resource name of the `WorkerPool` being updated. Format: `projects/{project}/locations/{location}/workerPools/{worker_pool}`.", "type": "string"}}, "type": "object"}, "UploadedMavenArtifact": {"description": "A Maven artifact uploaded using the MavenArtifact directive.", "id": "UploadedMavenArtifact", "properties": {"fileHashes": {"$ref": "FileHashes", "description": "Hash types and values of the Maven Artifact."}, "pushTiming": {"$ref": "TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded artifact.", "type": "string"}}, "type": "object"}, "UploadedPythonPackage": {"description": "Artifact uploaded using the PythonPackage directive.", "id": "UploadedPythonPackage", "properties": {"fileHashes": {"$ref": "FileHashes", "description": "Hash types and values of the Python Artifact."}, "pushTiming": {"$ref": "TimeSpan", "description": "Output only. Stores timing information for pushing the specified artifact.", "readOnly": true}, "uri": {"description": "URI of the uploaded artifact.", "type": "string"}}, "type": "object"}, "Volume": {"description": "Volume describes a Docker container volume which is mounted into build steps in order to persist files across build step execution.", "id": "Volume", "properties": {"name": {"description": "Name of the volume to mount. Volume names must be unique per build step and must be valid names for Docker volumes. Each named volume must be used by at least two build steps.", "type": "string"}, "path": {"description": "Path at which to mount the volume. Paths must be absolute and cannot conflict with other volume paths on the same build step or with certain reserved volume paths.", "type": "string"}}, "type": "object"}, "Warning": {"description": "A non-fatal problem encountered during the execution of the build.", "id": "Warning", "properties": {"priority": {"description": "The priority for this warning.", "enum": ["PRIORITY_UNSPECIFIED", "INFO", "WARNING", "ALERT"], "enumDescriptions": ["Should not be used.", "e.g. deprecation warnings and alternative feature highlights.", "e.g. automated detection of possible issues with the build.", "e.g. alerts that a feature used in the build is pending removal"], "type": "string"}, "text": {"description": "Explanation of the warning generated.", "type": "string"}}, "type": "object"}, "WorkerConfig": {"description": "WorkerConfig defines the configuration to be used for a creating workers in the pool.", "id": "WorkerConfig", "properties": {"diskSizeGb": {"description": "Size of the disk attached to the worker, in GB. See https://cloud.google.com/compute/docs/disks/ If `0` is specified, Cloud Build will use a standard disk size. `disk_size` is overridden if you specify a different disk size in `build_options`. In this case, a VM with a disk size specified in the `build_options` will be created on demand at build time. For more information see https://cloud.google.com/cloud-build/docs/api/reference/rest/v1/projects.builds#buildoptions", "format": "int64", "type": "string"}, "machineType": {"description": "Machine Type of the worker, such as n1-standard-1. See https://cloud.google.com/compute/docs/machine-types. If left blank, Cloud Build will use a standard unspecified machine to create the worker pool. `machine_type` is overridden if you specify a different machine type in `build_options`. In this case, the VM specified in the `build_options` will be created on demand at build time. For more information see https://cloud.google.com/cloud-build/docs/speeding-up-builds#using_custom_virtual_machine_sizes", "type": "string"}, "network": {"$ref": "Network", "description": "The network definition used to create the worker. If this section is left empty, the workers will be created in WorkerPool.project_id on the default network."}, "tag": {"description": "The tag applied to the worker, and the same tag used by the firewall rule. It is used to identify the Cloud Build workers among other VMs. The default value for tag is `worker`.", "type": "string"}}, "type": "object"}, "WorkerPool": {"description": "Configuration for a WorkerPool to run the builds. Workers are machines that Cloud Build uses to run your builds. By default, all workers run in a project owned by Cloud Build. To have full control over the workers that execute your builds -- such as enabling them to access private resources on your private network -- you can request Cloud Build to run the workers in your own project by creating a custom workers pool.", "id": "WorkerPool", "properties": {"createTime": {"description": "Output only. Time at which the request to create the `WorkerPool` was received.", "format": "google-datetime", "type": "string"}, "deleteTime": {"description": "Output only. Time at which the request to delete the `WorkerPool` was received.", "format": "google-datetime", "type": "string"}, "name": {"description": "User-defined name of the `WorkerPool`.", "type": "string"}, "projectId": {"description": "The project ID of the GCP project for which the `WorkerPool` is created.", "type": "string"}, "regions": {"description": "List of regions to create the `WorkerPool`. Regions can't be empty. If Cloud Build adds a new GCP region in the future, the existing `WorkerPool` will not be enabled in the new region automatically; you must add the new region to the `regions` field to enable the `WorkerPool` in that region.", "items": {"enum": ["REGION_UNSPECIFIED", "us-central1", "us-west1", "us-east1", "us-east4"], "enumDescriptions": ["no region", "us-central1 region", "us-west1 region", "us-east1 region", "us-east4 region"], "type": "string"}, "type": "array"}, "serviceAccountEmail": {"description": "Output only. The service account used to manage the `WorkerPool`. The service account must have the Compute Instance Admin (Beta) permission at the project level.", "type": "string"}, "status": {"description": "Output only. WorkerPool Status.", "enum": ["STATUS_UNSPECIFIED", "CREATING", "RUNNING", "DELETING", "DELETED"], "enumDescriptions": ["Status of the `WorkerPool` is unknown.", "`WorkerPool` is being created.", "`Worker<PERSON>ool` is running.", "`WorkerPool` is being deleted: cancelling builds and draining workers.", "`WorkerPool` is deleted."], "type": "string"}, "updateTime": {"description": "Output only. Time at which the request to update the `WorkerPool` was received.", "format": "google-datetime", "type": "string"}, "workerConfig": {"$ref": "WorkerConfig", "description": "Configuration to be used for a creating workers in the `WorkerPool`."}, "workerCount": {"description": "Total number of workers to be created across all requested regions.", "format": "int64", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Build API", "version": "v1alpha1", "version_module": true}