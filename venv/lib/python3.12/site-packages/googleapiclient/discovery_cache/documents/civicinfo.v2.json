{"basePath": "", "baseUrl": "https://civicinfo.googleapis.com/", "batchPath": "batch", "canonicalName": "Civic Info", "description": "Provides polling places, early vote locations, contest data, election officials, and government representatives for U.S. residential addresses.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/civic-information/", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "civicinfo:v2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://civicinfo.mtls.googleapis.com/", "name": "civicinfo", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"divisions": {"methods": {"queryDivisionByAddress": {"description": "Lookup OCDIDs and names for divisions related to an address.", "flatPath": "civicinfo/v2/divisionsByAddress", "httpMethod": "GET", "id": "civicinfo.divisions.queryDivisionByAddress", "parameterOrder": [], "parameters": {"address": {"location": "query", "type": "string"}}, "path": "civicinfo/v2/divisionsByAddress", "response": {"$ref": "CivicinfoApiprotosV2DivisionByAddressResponse"}}, "search": {"description": "Searches for political divisions by their natural name or OCD ID.", "flatPath": "civicinfo/v2/divisions", "httpMethod": "GET", "id": "civicinfo.divisions.search", "parameterOrder": [], "parameters": {"query": {"description": "The search query. Queries can cover any parts of a OCD ID or a human readable division name. All words given in the query are treated as required patterns. In addition to that, most query operators of the Apache Lucene library are supported. See http://lucene.apache.org/core/2_9_4/queryparsersyntax.html", "location": "query", "type": "string"}}, "path": "civicinfo/v2/divisions", "response": {"$ref": "CivicinfoApiprotosV2DivisionSearchResponse"}}}}, "elections": {"methods": {"electionQuery": {"description": "List of available elections to query.", "flatPath": "civicinfo/v2/elections", "httpMethod": "GET", "id": "civicinfo.elections.electionQuery", "parameterOrder": [], "parameters": {"productionDataOnly": {"default": "true", "description": "Whether to include data that has not been allowlisted yet", "location": "query", "type": "boolean"}}, "path": "civicinfo/v2/elections", "response": {"$ref": "CivicinfoApiprotosV2ElectionsQueryResponse"}}, "voterInfoQuery": {"description": "Looks up information relevant to a voter based on the voter's registered address.", "flatPath": "civicinfo/v2/voterinfo", "httpMethod": "GET", "id": "civicinfo.elections.voterInfoQuery", "parameterOrder": [], "parameters": {"address": {"description": "The registered address of the voter to look up.", "location": "query", "type": "string"}, "electionId": {"default": "0", "description": "The unique ID of the election to look up. A list of election IDs can be obtained at https://www.googleapis.com/civicinfo/{version}/elections. If no election ID is specified in the query and there is more than one election with data for the given voter, the additional elections are provided in the otherElections response field.", "format": "int64", "location": "query", "type": "string"}, "officialOnly": {"default": "false", "description": "If set to true, only data from official state sources will be returned.", "location": "query", "type": "boolean"}, "productionDataOnly": {"default": "true", "description": "Whether to include data that has not been vetted yet. Should only be made available to internal IPs or trusted partners. This is a non-discoverable parameter in the One Platform API config.", "location": "query", "type": "boolean"}, "returnAllAvailableData": {"default": "false", "description": "If set to true, the query will return the success code and include any partial information when it is unable to determine a matching address or unable to determine the election for electionId=0 queries.", "location": "query", "type": "boolean"}}, "path": "civicinfo/v2/voterinfo", "response": {"$ref": "CivicinfoApiprotosV2VoterInfoResponse"}}}}}, "revision": "20250430", "rootUrl": "https://civicinfo.googleapis.com/", "schemas": {"CivicinfoApiprotosV2DivisionByAddressResponse": {"id": "CivicinfoApiprotosV2DivisionByAddressResponse", "properties": {"divisions": {"additionalProperties": {"$ref": "CivicinfoSchemaV2GeographicDivision"}, "type": "object"}, "normalizedInput": {"$ref": "CivicinfoSchemaV2SimpleAddressType", "description": "The normalized version of the requested address."}}, "type": "object"}, "CivicinfoApiprotosV2DivisionSearchResponse": {"description": "The result of a division search query.", "id": "CivicinfoApiprotosV2DivisionSearchResponse", "properties": {"kind": {"default": "civicinfo#divisionSearchResponse", "description": "Identifies what kind of resource this is. Value: the fixed string \"civicinfo#divisionSearchResponse\".", "type": "string"}, "results": {"items": {"$ref": "CivicinfoApiprotosV2DivisionSearchResult"}, "type": "array"}}, "type": "object"}, "CivicinfoApiprotosV2DivisionSearchResult": {"description": "Represents a political geographic division that matches the requested query.", "id": "CivicinfoApiprotosV2DivisionSearchResult", "properties": {"aliases": {"description": "Other Open Civic Data identifiers that refer to the same division -- for example, those that refer to other political divisions whose boundaries are defined to be coterminous with this one. For example, ocd-division/country:us/state:wy will include an alias of ocd-division/country:us/state:wy/cd:1, since Wyoming has only one Congressional district.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "The name of the division.", "type": "string"}, "ocdId": {"description": "The unique Open Civic Data identifier for this division", "type": "string"}}, "type": "object"}, "CivicinfoApiprotosV2ElectionsQueryResponse": {"description": "The list of elections available for this version of the API.", "id": "CivicinfoApiprotosV2ElectionsQueryResponse", "properties": {"elections": {"description": "A list of available elections", "items": {"$ref": "CivicinfoSchemaV2Election"}, "type": "array"}, "kind": {"default": "civicinfo#electionsQueryResponse", "description": "Identifies what kind of resource this is. Value: the fixed string \"civicinfo#electionsQueryResponse\".", "type": "string"}}, "type": "object"}, "CivicinfoApiprotosV2VoterInfoResponse": {"description": "The result of a voter info lookup query.", "id": "CivicinfoApiprotosV2VoterInfoResponse", "properties": {"contests": {"description": "Contests that will appear on the voter's ballot.", "items": {"$ref": "CivicinfoSchemaV2Contest"}, "type": "array"}, "dropOffLocations": {"description": "Locations where a voter is eligible to drop off a completed ballot. The voter must have received and completed a ballot prior to arriving at the location. The location may not have ballots available on the premises. These locations could be open on or before election day as indicated in the pollingHours field.", "items": {"$ref": "CivicinfoSchemaV2PollingLocation"}, "type": "array"}, "earlyVoteSites": {"description": "Locations where the voter is eligible to vote early, prior to election day.", "items": {"$ref": "CivicinfoSchemaV2PollingLocation"}, "type": "array"}, "election": {"$ref": "CivicinfoSchemaV2Election", "description": "The election that was queried."}, "kind": {"default": "civicinfo#voterInfoResponse", "description": "Identifies what kind of resource this is. Value: the fixed string \"civicinfo#voterInfoResponse\".", "type": "string"}, "mailOnly": {"description": "Specifies whether voters in the precinct vote only by mailing their ballots (with the possible option of dropping off their ballots as well).", "type": "boolean"}, "normalizedInput": {"$ref": "CivicinfoSchemaV2SimpleAddressType", "description": "The normalized version of the requested address"}, "otherElections": {"description": "When there are multiple elections for a voter address, the otherElections field is populated in the API response and there are two possibilities: 1. If the earliest election is not the intended election, specify the election ID of the desired election in a second API request using the electionId field. 2. If these elections occur on the same day, the API doesn?t return any polling location, contest, or election official information to ensure that an additional query is made. For user-facing applications, we recommend displaying these elections to the user to disambiguate. A second API request using the electionId field should be made for the election that is relevant to the user.", "items": {"$ref": "CivicinfoSchemaV2Election"}, "type": "array"}, "pollingLocations": {"description": "Locations where the voter is eligible to vote on election day.", "items": {"$ref": "CivicinfoSchemaV2PollingLocation"}, "type": "array"}, "precinctId": {"type": "string"}, "precincts": {"description": "The precincts that match this voter's address. Will only be returned for project IDs which have been allowlisted as \"partner projects\".", "items": {"$ref": "CivicinfoSchemaV2Precinct"}, "type": "array"}, "state": {"description": "Local Election Information for the state that the voter votes in. For the US, there will only be one element in this array.", "items": {"$ref": "CivicinfoSchemaV2AdministrationRegion"}, "type": "array"}}, "type": "object"}, "CivicinfoSchemaV2AdministrationRegion": {"description": "Describes information about a regional election administrative area.", "id": "CivicinfoSchemaV2AdministrationRegion", "properties": {"electionAdministrationBody": {"$ref": "CivicinfoSchemaV2AdministrativeBody", "description": "The election administration body for this area."}, "local_jurisdiction": {"$ref": "CivicinfoSchemaV2AdministrationRegion", "description": "The city or county that provides election information for this voter. This object can have the same elements as state."}, "name": {"description": "The name of the jurisdiction.", "type": "string"}, "sources": {"description": "A list of sources for this area. If multiple sources are listed the data has been aggregated from those sources.", "items": {"$ref": "CivicinfoSchemaV2Source"}, "type": "array"}}, "type": "object"}, "CivicinfoSchemaV2AdministrativeBody": {"description": "Information about an election administrative body (e.g. County Board of Elections).", "id": "CivicinfoSchemaV2AdministrativeBody", "properties": {"absenteeVotingInfoUrl": {"description": "A URL provided by this administrative body for information on absentee voting.", "type": "string"}, "ballotInfoUrl": {"description": "A URL provided by this administrative body to give contest information to the voter.", "type": "string"}, "correspondenceAddress": {"$ref": "CivicinfoSchemaV2SimpleAddressType", "description": "The mailing address of this administrative body."}, "electionInfoUrl": {"description": "A URL provided by this administrative body for looking up general election information.", "type": "string"}, "electionNoticeText": {"description": "A last minute or emergency notification text provided by this administrative body.", "type": "string"}, "electionNoticeUrl": {"description": "A URL provided by this administrative body for additional information related to the last minute or emergency notification.", "type": "string"}, "electionOfficials": {"description": "The election officials for this election administrative body.", "items": {"$ref": "CivicinfoSchemaV2ElectionOfficial"}, "type": "array"}, "electionRegistrationConfirmationUrl": {"description": "A URL provided by this administrative body for confirming that the voter is registered to vote.", "type": "string"}, "electionRegistrationUrl": {"description": "A URL provided by this administrative body for looking up how to register to vote.", "type": "string"}, "electionRulesUrl": {"description": "A URL provided by this administrative body describing election rules to the voter.", "type": "string"}, "hoursOfOperation": {"description": "A description of the hours of operation for this administrative body.", "type": "string"}, "name": {"description": "The name of this election administrative body.", "type": "string"}, "physicalAddress": {"$ref": "CivicinfoSchemaV2SimpleAddressType", "description": "The physical address of this administrative body."}, "voter_services": {"description": "A description of the services this administrative body may provide.", "items": {"type": "string"}, "type": "array"}, "votingLocationFinderUrl": {"description": "A URL provided by this administrative body for looking up where to vote.", "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2Candidate": {"description": "Information about a candidate running for elected office.", "id": "CivicinfoSchemaV2Candidate", "properties": {"candidateUrl": {"description": "The URL for the candidate's campaign web site.", "type": "string"}, "channels": {"description": "A list of known (social) media channels for this candidate.", "items": {"$ref": "CivicinfoSchemaV2Channel"}, "type": "array"}, "email": {"description": "The email address for the candidate's campaign.", "type": "string"}, "name": {"description": "The candidate's name. If this is a joint ticket it will indicate the name of the candidate at the top of a ticket followed by a / and that name of candidate at the bottom of the ticket. e.g. \"<PERSON><PERSON> / <PERSON>\"", "type": "string"}, "orderOnBallot": {"description": "The order the candidate appears on the ballot for this contest.", "format": "int64", "type": "string"}, "party": {"description": "The full name of the party the candidate is a member of.", "type": "string"}, "phone": {"description": "The voice phone number for the candidate's campaign office.", "type": "string"}, "photoUrl": {"description": "A URL for a photo of the candidate.", "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2Channel": {"description": "A social media or web channel for a candidate.", "id": "CivicinfoSchemaV2Channel", "properties": {"id": {"description": "The unique public identifier for the candidate's channel.", "type": "string"}, "type": {"description": "The type of channel. The following is a list of types of channels, but is not exhaustive. More channel types may be added at a later time. One of: GooglePlus, YouTube, Facebook, Twitter", "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2Contest": {"description": "Information about a contest that appears on a voter's ballot.", "id": "CivicinfoSchemaV2Contest", "properties": {"ballotPlacement": {"description": "A number specifying the position of this contest on the voter's ballot.", "format": "int64", "type": "string"}, "ballotTitle": {"description": "The official title on the ballot for this contest, only where available.", "type": "string"}, "candidates": {"description": "The candidate choices for this contest.", "items": {"$ref": "CivicinfoSchemaV2Candidate"}, "type": "array"}, "district": {"$ref": "CivicinfoSchemaV2ElectoralDistrict", "description": "Information about the electoral district that this contest is in."}, "electorateSpecifications": {"description": "A description of any additional eligibility requirements for voting in this contest.", "type": "string"}, "level": {"description": "The levels of government of the office for this contest. There may be more than one in cases where a jurisdiction effectively acts at two different levels of government; for example, the mayor of the District of Columbia acts at \"locality\" level, but also effectively at both \"administrative-area-2\" and \"administrative-area-1\".", "items": {"enum": ["international", "country", "administrativeArea1", "regional", "administrativeArea2", "locality", "subLocality1", "subLocality2", "special"], "enumDescriptions": ["", "", "", "", "", "", "", "", ""], "type": "string"}, "type": "array"}, "numberElected": {"description": "The number of candidates that will be elected to office in this contest.", "format": "int64", "type": "string"}, "numberVotingFor": {"description": "The number of candidates that a voter may vote for in this contest.", "format": "int64", "type": "string"}, "office": {"description": "The name of the office for this contest.", "type": "string"}, "primaryParties": {"description": "If this is a partisan election, the name of the party/parties it is for.", "items": {"type": "string"}, "type": "array"}, "referendumBallotResponses": {"description": "The set of ballot responses for the referendum. A ballot response represents a line on the ballot. Common examples might include \"yes\" or \"no\" for referenda. This field is only populated for contests of type 'Referendum'.", "items": {"type": "string"}, "type": "array"}, "referendumBrief": {"description": "Specifies a short summary of the referendum that is typically on the ballot below the title but above the text. This field is only populated for contests of type 'Referendum'.", "type": "string"}, "referendumConStatement": {"description": "A statement in opposition to the referendum. It does not necessarily appear on the ballot. This field is only populated for contests of type 'Referendum'.", "type": "string"}, "referendumEffectOfAbstain": {"description": "Specifies what effect abstaining (not voting) on the proposition will have (i.e. whether abstaining is considered a vote against it). This field is only populated for contests of type 'Referendum'.", "type": "string"}, "referendumPassageThreshold": {"description": "The threshold of votes that the referendum needs in order to pass, e.g. \"two-thirds\". This field is only populated for contests of type 'Referendum'.", "type": "string"}, "referendumProStatement": {"description": "A statement in favor of the referendum. It does not necessarily appear on the ballot. This field is only populated for contests of type 'Referendum'.", "type": "string"}, "referendumSubtitle": {"description": "A brief description of the referendum. This field is only populated for contests of type 'Referendum'.", "type": "string"}, "referendumText": {"description": "The full text of the referendum. This field is only populated for contests of type 'Referendum'.", "type": "string"}, "referendumTitle": {"description": "The title of the referendum (e.g. 'Proposition 42'). This field is only populated for contests of type 'Referendum'.", "type": "string"}, "referendumUrl": {"description": "A link to the referendum. This field is only populated for contests of type 'Referendum'.", "type": "string"}, "roles": {"description": "The roles which this office fulfills.", "items": {"enum": ["headOfState", "headOfGovernment", "deputyHeadOfGovernment", "governmentOfficer", "executive<PERSON><PERSON><PERSON><PERSON>", "legislator<PERSON>pper<PERSON><PERSON>", "legislatorLowerBody", "highestCourtJudge", "judge", "schoolBoard", "specialPurposeOfficer", "otherRole"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", ""], "type": "string"}, "type": "array"}, "sources": {"description": "A list of sources for this contest. If multiple sources are listed, the data has been aggregated from those sources.", "items": {"$ref": "CivicinfoSchemaV2Source"}, "type": "array"}, "special": {"description": "\"Yes\" or \"No\" depending on whether this a contest being held outside the normal election cycle.", "type": "string"}, "type": {"description": "The type of contest. Usually this will be 'General', 'Primary', or 'Run-off' for contests with candidates. For referenda this will be 'Referendum'. For Retention contests this will typically be 'Retention'.", "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2Election": {"description": "Information about the election that was queried.", "id": "CivicinfoSchemaV2Election", "properties": {"electionDay": {"description": "Day of the election in YYYY-MM-DD format.", "type": "string"}, "id": {"description": "The unique ID of this election.", "format": "int64", "type": "string"}, "name": {"description": "A displayable name for the election.", "type": "string"}, "ocdDivisionId": {"description": "The political division of the election. Represented as an OCD Division ID. Voters within these political jurisdictions are covered by this election. This is typically a state such as ocd-division/country:us/state:ca or for the midterms or general election the entire US (i.e. ocd-division/country:us).", "type": "string"}, "shapeLookupBehavior": {"enum": ["shapeLookupDefault", "shapeLookupDisabled", "shapeLookupEnabled"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2ElectionOfficial": {"description": "Information about individual election officials.", "id": "CivicinfoSchemaV2ElectionOfficial", "properties": {"emailAddress": {"description": "The email address of the election official.", "type": "string"}, "faxNumber": {"description": "The fax number of the election official.", "type": "string"}, "name": {"description": "The full name of the election official.", "type": "string"}, "officePhoneNumber": {"description": "The office phone number of the election official.", "type": "string"}, "title": {"description": "The title of the election official.", "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2ElectoralDistrict": {"description": "Describes the geographic scope of a contest.", "id": "CivicinfoSchemaV2ElectoralDistrict", "properties": {"id": {"description": "An identifier for this district, relative to its scope. For example, the 34th State Senate district would have id \"34\" and a scope of stateUpper.", "type": "string"}, "name": {"description": "The name of the district.", "type": "string"}, "scope": {"description": "The geographic scope of this district. If unspecified the district's geography is not known. One of: national, statewide, congressional, stateUpper, stateLower, countywide, judicial, schoolBoard, cityWide, township, countyCouncil, cityCouncil, ward, special", "enum": ["statewide", "congressional", "stateUpper", "stateLower", "countywide", "judicial", "schoolBoard", "citywide", "special", "countyCouncil", "township", "ward", "cityCouncil", "national"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2GeographicDivision": {"description": "Describes a political geography.", "id": "CivicinfoSchemaV2GeographicDivision", "properties": {"alsoKnownAs": {"description": "Any other valid OCD IDs that refer to the same division.\\n\\nBecause OCD IDs are meant to be human-readable and at least somewhat predictable, there are occasionally several identifiers for a single division. These identifiers are defined to be equivalent to one another, and one is always indicated as the primary identifier. The primary identifier will be returned in ocd_id above, and any other equivalent valid identifiers will be returned in this list.\\n\\nFor example, if this division's OCD ID is ocd-division/country:us/district:dc, this will contain ocd-division/country:us/state:dc.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "The name of the division.", "type": "string"}, "officeIndices": {"description": "List of indices in the offices array, one for each office elected from this division. Will only be present if includeOffices was true (or absent) in the request.", "items": {"format": "uint32", "type": "integer"}, "type": "array"}}, "type": "object"}, "CivicinfoSchemaV2PollingLocation": {"description": "A location where a voter can vote. This may be an early vote site, an election day voting location, or a drop off location for a completed ballot.", "id": "CivicinfoSchemaV2PollingLocation", "properties": {"address": {"$ref": "CivicinfoSchemaV2SimpleAddressType", "description": "The address of the location."}, "endDate": {"description": "The last date that this early vote site or drop off location may be used. This field is not populated for polling locations.", "type": "string"}, "latitude": {"description": "Latitude of the location, in degrees north of the equator. Note this field may not be available for some locations.", "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the location, in degrees east of the Prime Meridian. Note this field may not be available for some locations.", "format": "double", "type": "number"}, "name": {"description": "The name of the early vote site or drop off location. This field is not populated for polling locations.", "type": "string"}, "notes": {"description": "Notes about this location (e.g. accessibility ramp or entrance to use).", "type": "string"}, "pollingHours": {"description": "A description of when this location is open.", "type": "string"}, "sources": {"description": "A list of sources for this location. If multiple sources are listed the data has been aggregated from those sources.", "items": {"$ref": "CivicinfoSchemaV2Source"}, "type": "array"}, "startDate": {"description": "The first date that this early vote site or drop off location may be used. This field is not populated for polling locations.", "type": "string"}, "voterServices": {"description": "The services provided by this early vote site or drop off location. This field is not populated for polling locations.", "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2Precinct": {"id": "CivicinfoSchemaV2Precinct", "properties": {"administrationRegionId": {"description": "ID of the AdministrationRegion message for this precinct. Corresponds to LocalityId xml tag.", "type": "string"}, "contestId": {"description": "ID(s) of the Contest message(s) for this precinct.", "items": {"type": "string"}, "type": "array"}, "datasetId": {"description": "Required. Dataset ID. What datasets our Precincts come from.", "format": "int64", "type": "string"}, "earlyVoteSiteId": {"description": "ID(s) of the PollingLocation message(s) for this precinct.", "items": {"type": "string"}, "type": "array"}, "electoralDistrictId": {"description": "ID(s) of the ElectoralDistrict message(s) for this precinct.", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Required. A unique identifier for this precinct.", "type": "string"}, "mailOnly": {"description": "Specifies if the precinct runs mail-only elections.", "type": "boolean"}, "name": {"description": "Required. The name of the precinct.", "type": "string"}, "number": {"description": "The number of the precinct.", "type": "string"}, "ocdId": {"description": "Encouraged. The OCD ID of the precinct", "items": {"type": "string"}, "type": "array"}, "pollingLocationId": {"description": "ID(s) of the PollingLocation message(s) for this precinct.", "items": {"type": "string"}, "type": "array"}, "spatialBoundaryId": {"description": "ID(s) of the SpatialBoundary message(s) for this precinct. Used to specify a geometrical boundary of the precinct.", "items": {"type": "string"}, "type": "array"}, "splitName": {"description": "If present, this proto corresponds to one portion of split precinct. Other portions of this precinct are guaranteed to have the same `name`. If not present, this proto represents a full precicnt.", "type": "string"}, "ward": {"description": "Specifies the ward the precinct is contained within.", "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2SimpleAddressType": {"description": "A simple representation of an address.", "id": "CivicinfoSchemaV2SimpleAddressType", "properties": {"city": {"description": "The city or town for the address.", "type": "string"}, "line1": {"description": "The street name and number of this address.", "type": "string"}, "line2": {"description": "The second line the address, if needed.", "type": "string"}, "line3": {"description": "The third line of the address, if needed.", "type": "string"}, "locationName": {"description": "The name of the location.", "type": "string"}, "state": {"description": "The US two letter state abbreviation of the address.", "type": "string"}, "zip": {"description": "The US Postal Zip Code of the address.", "type": "string"}}, "type": "object"}, "CivicinfoSchemaV2Source": {"description": "Contains information about the data source for the element containing it.", "id": "CivicinfoSchemaV2Source", "properties": {"name": {"description": "The name of the data source.", "type": "string"}, "official": {"description": "Whether this data comes from an official government source.", "type": "boolean"}}, "type": "object"}}, "servicePath": "", "title": "Google Civic Information API", "version": "v2", "version_module": true}